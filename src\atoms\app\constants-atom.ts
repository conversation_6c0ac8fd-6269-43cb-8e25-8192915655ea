import type { ConstantsData, GetConstantsResponse } from "@/types/helpers";
import { endpoint } from "@/utils/endpoints";
import { atom } from "@mongez/react-atom";
import { AxiosError } from "axios";
import toast from "react-hot-toast";

interface ConstantsAtom {
  constants: ConstantsData | null;
}

interface ConstantsAtomAction {
  getConstants: () => void;
}

export const constantsAtom = atom<ConstantsAtom, ConstantsAtomAction>({
  key: "constants-atom",
  default: {
    constants: null,
  },

  actions: {
    async getConstants() {
      try {
        const { data } = await endpoint.post<GetConstantsResponse>(
          "helpers/fetch-constants",
          {
            types: [
              "business",
              "devices",
              "engine_hours",
              "fuels",
              "user_types",
              "areas_types",
            ],
          },
        );
        constantsAtom.change("constants", data.data);
      } catch (error) {
        if (error instanceof AxiosError) {
          toast.error(error.response?.data.message);
        } else {
          toast.error((error as Error).message);
        }
      }
    },
  },
});
