export const usersTranslations = {
  confirm_delete: {
    deleteUserTitle: "Delete User",
    deleteUserDescription:
      "Are you sure you want to delete this user? This action cannot be undone.",
  },

  createUser: {
    title: "Create User",
    description: "Fill in the details to create a new user.",
    fields: {
      password_confirmation: "Confirm Password",
      phone_code: "Phone Code",
      lang: "Language",
    },
    placeholders: {
      name: "Enter full name",
      email: "Enter email",
      password: "Enter password",
      password_confirmation: "Confirm password",
      phone: "5012121212",
      lang: "Select language",
      phone_code: "Select code",
    },
  },

  editUser: {
    title: "Update User",
    description: "Fill in the details to update the user.",
  },

  options: {
    phoneCodes: {
      "966": "+966 (Saudi Arabia)",
      "20": "+20 (Egypt)",
      "971": "+971 (UAE)",
      "1": "+1 (USA)",
    },
    languages: {
      en: "English",
      ar: "Arabic",
    },
  },

  header: {
    title: "Users",
    add: "Add User",
  },

  table: {
    selectAll: "Select all",
    selectRow: "Select row",
    email: "Email",
    phone: "Phone",
    isActive: "Is Active",
  },

  actions: {
    title: "Actions",
  },

  validation: {
    nameRequired: "Name is required",
    emailInvalid: "Invalid email",
    passwordMin: "Password must be at least 8 characters",
    passwordConfirmMin: "Password confirmation must be at least 8 characters",
    passwordsMismatch: "Passwords do not match",
    phoneCodeRequired: "Phone code is required",
    phoneRequired: "Phone number is required",
    langRequired: "Language is required",
  },
};
