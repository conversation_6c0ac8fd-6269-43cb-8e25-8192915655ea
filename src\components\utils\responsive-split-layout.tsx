import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "@/components/ui/resizable";
import { useIsMobile } from "@/hooks/use-mobile";
import useTableResize from "@/hooks/use-table-resize";
import { type ReactNode } from "react";

type ResponsiveSplitLayoutProps = {
  header: ReactNode;
  leftContent: ReactNode;
  rightContent: ReactNode;
  dialogs?: ReactNode;
};

export default function ResponsiveSplitLayout({
  header,
  leftContent,
  rightContent,
  dialogs,
}: ResponsiveSplitLayoutProps) {
  const isMobile = useIsMobile();
  const { handleResize, tableSize, minSize } = useTableResize();

  if (isMobile) {
    return (
      <div className="flex flex-col gap-6">
        <div className="flex flex-col gap-4 p-2">
          {header}
          <div className="p-4">{leftContent}</div>
        </div>
        <div className="h-[400px] w-full p-4">{rightContent}</div>
        {dialogs}
      </div>
    );
  }

  return (
    <ResizablePanelGroup direction="horizontal">
      <ResizablePanel
        className="flex flex-col gap-4 rounded-xl"
        defaultSize={tableSize}
        onResize={handleResize}
        minSize={minSize}
      >
        {header}
        {leftContent}
      </ResizablePanel>

      <ResizableHandle withHandle className="mx-4" />

      <ResizablePanel className="rounded-xl">{rightContent}</ResizablePanel>

      {dialogs}
    </ResizablePanelGroup>
  );
}
