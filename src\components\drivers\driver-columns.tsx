import {
  openConfirmDeleteDriver<PERSON>tom,
  openUpdateDriverAtom,
} from "@/atoms/app/open-atoms";
import { selectedDriverAtom } from "@/atoms/app/selected-atoms";
import type { Driver } from "@/types/drivers";
import type { TFunction } from "i18next";
import { Edit, Trash2 } from "lucide-react";
import { createColumns } from "../utils/columns-factory";

export const DriverColumns = (t: TFunction) =>
  createColumns<Driver>({
    t,
    entityKey: "drivers",
    fields: [
      { key: "name", title: "common.table.name" },
      {
        key: "is_active",
        title: "drivers.table.isActive",
        cell: (driver) =>
          driver.is_active
            ? t("common.boolean.true")
            : t("common.boolean.false"),
      },
    ],
    actions: [
      {
        icon: <Edit />,
        label: t("common.actions.edit"),
        onClick: (row) => {
          selectedDriverAtom.change("slug", row.slug);
          openUpdateDriverAtom.open();
        },
      },
      {
        icon: <Trash2 color="red" />,
        label: t("common.actions.delete"),
        onClick: (row) => {
          selectedDriverAtom.change("slug", row.slug);
          openConfirmDeleteDriverAtom.open();
        },
      },
    ],
  });
