export const unitsTranslations = {
  header: {
    title: "الوحدات",
    add: "إنشاء وحدة",
  },

  list: {
    title: "قائمة الوحدات",
    noData: "لا توجد وحدات متاحة",
    searchPlaceholder: "ابحث عن وحدة...",
    actions: {
      title: "إجراءات",
      create: "إنشاء وحدة",
      update: "تعديل",
      delete: "حذف",
      view: "عرض",
    },
    table: {
      selectAll: "تحديد الكل",
      selectRow: "تحديد الصف",
      driver: "السائق",
      details: {
        model: "الموديل",
        year: "سنة الصنع",
        seats: "عدد المقاعد",
      },
      isActive: "مفعلة",
      actions: "إجراءات",
    },
  },

  create: {
    title: "إنشاء وحدة",
    description: "املأ التفاصيل لإنشاء وحدة جديدة.",
  },

  update: {
    title: "تعديل الوحدة",
    description: "قم بتحديث تفاصيل الوحدة.",
  },

  confirmDelete: {
    title: "تأكيد الحذف",
    description:
      "هل أنت متأكد أنك تريد حذف هذه الوحدة؟ لا يمكن التراجع عن هذا الإجراء.",
    buttons: {
      confirm: "نعم، احذف",
    },
  },

  fields: {
    plate_number: "رقم اللوحة",
    device_type: "نوع الجهاز",
    device_serial_number: "الرقم التسلسلي للجهاز",
    sim_card_number: "رقم شريحة SIM",
    sim_card_serial_number: "الرقم التسلسلي لشريحة SIM",
    imei: "IMEI",
    icon: "الأيقونة",
    operation_code: "رمز التشغيل",
    engine_hours_type: "نوع ساعات المحرك",
    engine_hours_value: "قيمة ساعات المحرك",
    odometer_type: "نوع عداد المسافات",
    odometer_val: "قيمة عداد المسافات",
    protocol_id: "البروتوكول",
    business_type: "نوع النشاط",
    vehicle_type: "نوع المركبة",
    measurement_type: "نوع القياس",
    max_capacity: "الحمولة القصوى",
    seats: "عدد المقاعد",
  },

  placeholders: {
    name: "أدخل اسم الوحدة",
    plate_number: "أدخل رقم اللوحة",
    device_type: "اختر نوع الجهاز",
    device_serial_number: "أدخل الرقم التسلسلي للجهاز",
    sim_card_number: "أدخل رقم شريحة SIM",
    sim_card_serial_number: "أدخل الرقم التسلسلي لشريحة SIM",
    imei: "أدخل IMEI",
    icon: "اختر أيقونة",
    password: "أدخل كلمة المرور",
    operation_code: "أدخل رمز التشغيل",
    engine_hours_type: "اختر نوع ساعات المحرك",
    engine_hours_value: "أدخل قيمة ساعات المحرك",
    odometer_type: "اختر نوع عداد المسافات",
    odometer_val: "أدخل قيمة عداد المسافات",
    protocol_id: "اختر البروتوكول",
    business_type: "اختر نوع النشاط",
    vehicle_type: "أدخل نوع المركبة",
    measurement_type: "اختر نوع القياس",
    max_capacity: "أدخل الحمولة القصوى",
    seats: "أدخل عدد المقاعد",
  },

  validation: {
    nameRequired: "اسم الوحدة مطلوب",
    plateNumberRequired: "رقم اللوحة مطلوب",
    deviceTypeRequired: "نوع الجهاز مطلوب",
    deviceSerialNumberRequired: "الرقم التسلسلي للجهاز مطلوب",
    simCardNumberRequired: "رقم شريحة SIM مطلوب",
    simCardSerialNumberRequired: "الرقم التسلسلي لشريحة SIM مطلوب",
    imeiRequired: "رقم IMEI مطلوب",
    iconRequired: "الأيقونة مطلوبة",
    passwordRequired: "كلمة المرور مطلوبة",
    passwordMin: "يجب أن تكون كلمة المرور 8 أحرف على الأقل",
    operationCodeRequired: "رمز التشغيل مطلوب",
    engineHoursTypeRequired: "نوع ساعات التشغيل مطلوب",
    engineHoursValueRequired: "قيمة ساعات التشغيل مطلوبة",
    engineHoursValuePositive: "يجب أن تكون قيمة ساعات التشغيل أكبر من 0",
    odometerTypeRequired: "نوع عداد المسافة مطلوب",
    odometerValRequired: "قيمة عداد المسافة مطلوبة",
    odometerValPositive: "يجب أن تكون قيمة عداد المسافة أكبر من 0",
    protocolIdRequired: "معرّف البروتوكول مطلوب",
    businessTypeRequired: "نوع النشاط مطلوب",
    vehicleTypeRequired: "نوع المركبة مطلوب",
    measurementTypeRequired: "نوع وحدة القياس مطلوب",
    maxCapacityRequired: "الحمولة القصوى مطلوبة",
    maxCapacityPositive: "يجب أن تكون الحمولة القصوى أكبر من 0",
    seatsRequired: "عدد المقاعد مطلوب",
    seatsPositive: "يجب أن يكون عدد المقاعد 1 على الأقل",
  },
};
