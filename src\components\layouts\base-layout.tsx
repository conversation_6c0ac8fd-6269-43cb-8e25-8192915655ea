import { modelsAtom } from "@/atoms/app/models-atom";
import { AppSidebar } from "@/components/app-sidebar";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { useEffect } from "react";
import { Outlet } from "react-router";
import Footer from "../footer";
import Navbar from "../navbar";

export default function BaseLayout() {
  useEffect(() => {
    modelsAtom.getModels();
  }, []);

  return (
    <SidebarProvider defaultOpen={true}>
      <AppSidebar />
      <SidebarInset className="gap-2.5 p-2.5">
        <Navbar />
        <div className="flex-1 overflow-hidden rounded-xl border">
          <Outlet />
        </div>
        <Footer />
      </SidebarInset>
    </SidebarProvider>
  );
}
