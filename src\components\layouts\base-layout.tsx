import { modelsAtom } from "@/atoms/app/models-atom";
import { AppSidebar } from "@/components/app-sidebar";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { useEffect } from "react";
import { Outlet } from "react-router";
import Footer from "../footer";
import Navbar from "../navbar";

export default function BaseLayout() {
  useEffect(() => {
    modelsAtom.getModels();
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      <SidebarProvider defaultOpen={true}>
        <AppSidebar />
        <SidebarInset className="gap-3 p-3">
          <Navbar />
          <div className="flex-1 overflow-hidden rounded-2xl border border-white/10 bg-gradient-to-br from-white/5 to-white/2 backdrop-blur-xl">
            <Outlet />
          </div>
          <Footer />
        </SidebarInset>
      </SidebarProvider>
    </div>
  );
}
