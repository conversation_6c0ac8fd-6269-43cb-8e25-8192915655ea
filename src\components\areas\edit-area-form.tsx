import { selectedDrawer<PERSON><PERSON><PERSON><PERSON> } from "@/atoms/app/selected-atoms";
import { But<PERSON> } from "@/components/ui/button";
import { ColorInput } from "@/components/ui/color-input";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Switch } from "@/components/ui/switch";
import useEditAreaForm from "@/hooks/areas/use-edit-area-form";
import { cn } from "@/lib/utils";
import { URLS } from "@/utils/urls";
import { Check, ChevronsUpDown } from "lucide-react";
import { Link } from "react-router";
import { OverlayLoader } from "../utils/overlay-loader";

export default function EditAreaForm() {
  const {
    areaGroups,
    form,
    isLoading,
    isLoadingFetchArea,
    onSubmit,
    t,
    types,
  } = useEditAreaForm();

  if (isLoadingFetchArea) {
    return (
      <div className="relative flex h-full items-center justify-center">
        <OverlayLoader />
      </div>
    );
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 gap-2 md:grid-cols-2">
          {/* Name */}
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("areas.form.name")}</FormLabel>
                <FormControl>
                  <Input
                    placeholder={t("areas.form.name_placeholder")}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Code */}
          <FormField
            control={form.control}
            name="code"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("areas.form.code")}</FormLabel>
                <FormControl>
                  <Input
                    placeholder={t("areas.form.code_placeholder")}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Type */}
          {/* <FormField
            control={form.control}
            name="type"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("areas.form.type")}</FormLabel>
                <Select
                  onValueChange={(value) => {
                    field.onChange(Number(value));
                    selectedDrawerTypeAtom.change(
                      "selectedType",
                      Number(value),
                    );
                  }}
                  value={field.value > 0 ? String(field.value) : ""}
                >
                  <FormControl>
                    <SelectTrigger className="w-full">
                      <SelectValue
                        placeholder={t("areas.form.type_placeholder")}
                      />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="1">{t("areas.form.circle")}</SelectItem>
                    <SelectItem value="2">{t("areas.form.polygon")}</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          /> */}

          <FormField
            control={form.control}
            name="type"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>{t("areas.form.type")}</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant="outline"
                        role="combobox"
                        className={cn(
                          "dark:bg-input/20 dark:border-input/30 w-full justify-between",
                          !field.value && "text-muted-foreground",
                        )}
                      >
                        {field.value
                          ? types.find((type) => type.id === field.value)?.label
                          : t("areas.form.type_placeholder")}
                        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0">
                    <Command>
                      <CommandInput
                        placeholder={t("areas.form.type_placeholder")}
                        className="h-9"
                      />
                      <CommandList>
                        <CommandEmpty>
                          {t("areas.form.no_type_found")}
                        </CommandEmpty>
                        <CommandGroup>
                          {types.map((type) => (
                            <CommandItem
                              key={type.id}
                              value={type.label}
                              onSelect={() => {
                                field.onChange(type.id);
                                selectedDrawerTypeAtom.change(
                                  "selectedType",
                                  type.id,
                                );
                              }}
                            >
                              <Check
                                className={cn(
                                  "mr-2 h-4 w-4",
                                  type.id === field.value
                                    ? "opacity-100"
                                    : "opacity-0",
                                )}
                              />
                              {type.label}
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Area Group */}
          {/* <FormField
            control={form.control}
            name="area_group_id"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("areas.form.area_group")}</FormLabel>
                <Select
                  onValueChange={(value) => field.onChange(Number(value))}
                  value={field.value > 0 ? String(field.value) : ""}
                >
                  <FormControl>
                    <SelectTrigger className="w-full">
                      <SelectValue
                        placeholder={t("areas.form.area_group_placeholder")}
                      />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {areaGroups?.map((group) => (
                      <SelectItem key={group.id} value={String(group.id)}>
                        {group.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          /> */}

          <FormField
            control={form.control}
            name="area_group_id"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>{t("areas.form.area_group")}</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant="outline"
                        role="combobox"
                        className={cn(
                          "dark:bg-input/20 dark:border-input/30 w-full justify-between",
                          !field.value && "text-muted-foreground",
                        )}
                      >
                        {field.value
                          ? areaGroups.find((g) => g.id === field.value)?.name
                          : t("areas.form.area_group_placeholder")}
                        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0">
                    <Command>
                      <CommandInput
                        placeholder={t("areas.form.area_group_placeholder")}
                        className="h-9"
                      />
                      <CommandList>
                        <CommandEmpty>
                          {t("areas.form.no_area_group_found")}
                        </CommandEmpty>
                        <CommandGroup>
                          {areaGroups?.map((group) => (
                            <CommandItem
                              key={group.id}
                              value={group.name}
                              onSelect={() => {
                                form.setValue("area_group_id", group.id);
                              }}
                            >
                              <Check
                                className={cn(
                                  "mr-2 h-4 w-4",
                                  group.id === field.value
                                    ? "opacity-100"
                                    : "opacity-0",
                                )}
                              />
                              {group.name}
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Color */}
          <FormField
            control={form.control}
            name="color"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t("areas.form.color")}</FormLabel>
                <FormControl>
                  <ColorInput
                    value={field.value}
                    onChange={field.onChange}
                    placeholder={t("areas.form.color_placeholder")}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Is Personal */}
        <FormField
          control={form.control}
          name="is_personal"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-2xl border p-3 shadow">
              <div className="space-y-0.5">
                <FormLabel>{t("areas.form.is_personal")}</FormLabel>
              </div>
              <FormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
            </FormItem>
          )}
        />

        {/* Buttons */}
        <div className="flex justify-end gap-2">
          <Button type="submit" disabled={isLoading} variant={"outline"}>
            {isLoading
              ? t("areas.edit_area.editing")
              : t("areas.edit_area.edit")}
          </Button>
          <Link to={URLS.areas}>
            <Button type="button" variant={"outline"}>
              {t("areas.form.back")}
            </Button>
          </Link>
        </div>
      </form>
    </Form>
  );
}
