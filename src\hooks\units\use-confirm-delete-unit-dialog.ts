import { openConfirmDeleteUnitAtom } from "@/atoms/app/open-atoms";
import { selectedUnitAtom } from "@/atoms/app/selected-atoms";
import { unitsAtom } from "@/atoms/entities/units-atom";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

export default function useConfirmDeleteUnitDialog() {
  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingFetchUnit, setIsLoadingFetchUnit] = useState(false);

  const { oneUnit } = unitsAtom.useValue();
  const { plate_number } = selectedUnitAtom.useValue();
  const isOpened = openConfirmDeleteUnitAtom.useOpened();

  const closeDialog = () => {
    openConfirmDeleteUnitAtom.close();
    selectedUnitAtom.reset();
    unitsAtom.change("oneUnit", null);
  };

  const deleteUnit = async () => {
    if (plate_number) {
      setIsLoading(true);
      await unitsAtom.deleteUnit(plate_number, closeDialog);
      setIsLoading(false);
    }
  };

  useEffect(() => {
    async function fetchUnit() {
      if (plate_number) {
        setIsLoadingFetchUnit(true);
        await unitsAtom.getOneUnit(plate_number);
        setIsLoadingFetchUnit(false);
      }
    }

    fetchUnit();
  }, [plate_number]);

  return {
    t,
    isLoading,
    isLoadingFetchUnit,
    oneUnit,
    isOpened,
    closeDialog,
    deleteUnit,
  };
}
