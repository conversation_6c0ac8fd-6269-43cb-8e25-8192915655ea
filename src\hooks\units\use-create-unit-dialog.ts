import { constants<PERSON>tom } from "@/atoms/app/constants-atom";
import { models<PERSON>tom } from "@/atoms/app/models-atom";
import { openCreateUnitAtom } from "@/atoms/app/open-atoms";
import { unitsAtom } from "@/atoms/entities/units-atom";
import type { CreateUnitFormData } from "@/types/units";
import { zodResolver } from "@hookform/resolvers/zod";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { z } from "zod";

export default function useCreateUnitDialog() {
  const { t } = useTranslation();

  // Create separate schemas for input (all strings) and output (transformed)
  const FormInputSchema = z.object({
    name: z.string().nonempty(t("units.validation.nameRequired")),
    plate_number: z
      .string()
      .nonempty(t("units.validation.plateNumberRequired")),
    device_type: z.string().nonempty(t("units.validation.deviceTypeRequired")),
    device_serial_number: z
      .string()
      .nonempty(t("units.validation.deviceSerialNumberRequired")),
    sim_card_number: z
      .string()
      .nonempty(t("units.validation.simCardNumberRequired")),
    sim_card_serial_number: z
      .string()
      .nonempty(t("units.validation.simCardSerialNumberRequired")),
    imei: z.string().nonempty(t("units.validation.imeiRequired")).max(15),
    password: z.string().nonempty(t("units.validation.passwordRequired")),
    operation_code: z
      .string()
      .nonempty(t("units.validation.operationCodeRequired")),
    engine_hours_type: z
      .string()
      .nonempty(t("units.validation.engineHoursTypeRequired")),
    engine_hours_value: z
      .string()
      .nonempty(t("units.validation.engineHoursValueRequired")),
    odometer_type: z
      .string()
      .nonempty(t("units.validation.odometerTypeRequired")),
    odometer_val: z
      .string()
      .nonempty(t("units.validation.odometerValRequired")),
    protocol_id: z.string().nonempty(t("units.validation.protocolIdRequired")),
    business_type: z
      .string()
      .nonempty(t("units.validation.businessTypeRequired")),
    vehicle_type: z
      .string()
      .nonempty(t("units.validation.vehicleTypeRequired")),
    measurement_type: z
      .string()
      .nonempty(t("units.validation.measurementTypeRequired")),
    max_capacity: z
      .string()
      .nonempty(t("units.validation.maxCapacityRequired")),
    seats: z.string().nonempty(t("units.validation.seatsRequired")),
  });

  // Output schema with transformations
  const FormOutputSchema = FormInputSchema.transform((data) => ({
    ...data,
    device_type: Number(data.device_type),
    engine_hours_type: Number(data.engine_hours_type),
    engine_hours_value: Number(data.engine_hours_value),
    odometer_type: Number(data.odometer_type),
    odometer_val: Number(data.odometer_val),
    protocol_id: Number(data.protocol_id),
    business_type: Number(data.business_type),
    measurement_type: Number(data.measurement_type),
    max_capacity: Number(data.max_capacity),
    seats: Number(data.seats),
  }));

  type FormInput = z.infer<typeof FormInputSchema>;

  const form = useForm<FormInput>({
    resolver: zodResolver(FormInputSchema), // Use input schema for validation
    defaultValues: {
      name: "",
      plate_number: "",
      device_type: "",
      device_serial_number: "",
      sim_card_number: "",
      sim_card_serial_number: "",
      imei: "",
      password: "",
      operation_code: "",
      engine_hours_type: "",
      engine_hours_value: "",
      odometer_type: "",
      odometer_val: "",
      protocol_id: "",
      business_type: "",
      vehicle_type: "",
      measurement_type: "",
      max_capacity: "",
      seats: "",
    },
  });

  const [isLoading, setIsLoading] = useState(false);
  const isOpened = openCreateUnitAtom.useOpened();
  const { constants } = constantsAtom.useValue();
  const { models } = modelsAtom.useValue();

  const closeDialog = () => {
    openCreateUnitAtom.close();
    form.reset();
  };

  async function onSubmit(inputData: FormInput) {
    // Parse and transform data using output schema
    const result = FormOutputSchema.safeParse(inputData);

    if (!result.success) {
      // Handle validation errors if needed
      return;
    }

    const formData: CreateUnitFormData = result.data;

    setIsLoading(true);

    await unitsAtom.createUnit(formData, () => {
      closeDialog();
    });

    setIsLoading(false);
  }

  return {
    form,
    isLoading,
    isOpened,
    closeDialog,
    onSubmit,
    t,
    constants,
    models,
  };
}
