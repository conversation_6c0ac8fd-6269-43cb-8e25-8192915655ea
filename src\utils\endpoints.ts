import { authAtom } from "@/atoms/app/auth-atom";
import Endpoint from "@mongez/http";
import { type AxiosResponse } from "axios";

export const endpoint = new Endpoint({
  baseURL: import.meta.env.VITE_API_URL,

  setAuthorizationHeader: () => {
    if (localStorage.getItem("gps-token")) {
      const token = localStorage.getItem("gps-token");
      return `Bearer ${token}`;
    }
  },

  headers: {
    "Accept-Language": localStorage.getItem("gps-lang") || "en", // default to English
    "API-Key": import.meta.env.VITE_API_KEY,
  },
});

endpoint.events.onError((response: AxiosResponse) => {
  if (response.data.status_code === 401) {
    authAtom.change("user", null);
    authAtom.change("token", "");
  }
});
