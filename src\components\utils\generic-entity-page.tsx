import MainMap from "@/components/main-map";
import { OverlayLoader } from "@/components/utils/overlay-loader";
import PageMainHeader from "@/components/utils/page-main-header";
import ResponsiveSplitLayout from "@/components/utils/responsive-split-layout";
import { type ReactNode, useEffect, useState } from "react";

type GenericEntityPageProps = {
  title: string;
  addText?: string;
  onAddClick?: () => void;
  addUrl?: string;
  leftContent: ReactNode;
  rightContent?: ReactNode;
  dialogs?: ReactNode;
  fetchers?: Array<() => void | Promise<void>>;
};

export default function GenericEntityPage({
  title,
  addText,
  onAddClick,
  leftContent,
  dialogs,
  fetchers = [],
  addUrl,
  rightContent,
}: GenericEntityPageProps) {
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    async function fetchAll() {
      if (fetchers.length === 0) return;
      setIsLoading(true);
      await Promise.all(fetchers.map((fn) => Promise.resolve(fn())));
      setIsLoading(false);
    }
    fetchAll();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (isLoading) return <OverlayLoader />;

  return (
    <div className="h-full p-6">
      {/* Glass morphism background */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-900/95 via-slate-800/90 to-slate-900/95" />

      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 h-80 w-80 animate-pulse rounded-full bg-gradient-to-br from-cyan-400/10 to-blue-500/10 blur-3xl" />
        <div className="absolute -bottom-40 -left-40 h-80 w-80 animate-pulse rounded-full bg-gradient-to-br from-purple-400/10 to-pink-500/10 blur-3xl" />
      </div>

      <div className="relative h-full">
        <ResponsiveSplitLayout
          header={
            <PageMainHeader
              title={title}
              addText={addText}
              onAddClick={onAddClick}
              addUrl={addUrl}
            />
          }
          leftContent={
            <div className="h-full rounded-2xl border border-white/10 bg-gradient-to-br from-white/10 to-white/5 p-6 backdrop-blur-xl">
              {leftContent}
            </div>
          }
          rightContent={
            <div className="h-full rounded-2xl border border-white/10 bg-gradient-to-br from-white/10 to-white/5 p-6 backdrop-blur-xl">
              {rightContent ?? <MainMap />}
            </div>
          }
          dialogs={dialogs}
        />
      </div>
    </div>
  );
}
