import MainMap from "@/components/main-map";
import { OverlayLoader } from "@/components/utils/overlay-loader";
import PageMainHeader from "@/components/utils/page-main-header";
import ResponsiveSplitLayout from "@/components/utils/responsive-split-layout";
import { type ReactNode, useEffect, useState } from "react";

type GenericEntityPageProps = {
  title: string;
  addText?: string;
  onAddClick?: () => void;
  addUrl?: string;
  leftContent: ReactNode;
  rightContent?: ReactNode;
  dialogs?: ReactNode;
  fetchers?: Array<() => void | Promise<void>>;
};

export default function GenericEntityPage({
  title,
  addText,
  onAddClick,
  leftContent,
  dialogs,
  fetchers = [],
  addUrl,
  rightContent,
}: GenericEntityPageProps) {
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    async function fetchAll() {
      if (fetchers.length === 0) return;
      setIsLoading(true);
      await Promise.all(fetchers.map((fn) => Promise.resolve(fn())));
      setIsLoading(false);
    }
    fetchAll();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (isLoading) return <OverlayLoader />;

  return (
    <div className="via-background/80 h-full rounded-xl bg-gradient-to-br from-fuchsia-500/10 to-cyan-400/10 p-6 shadow-2xl">
      <ResponsiveSplitLayout
        header={
          <PageMainHeader
            title={title}
            addText={addText}
            onAddClick={onAddClick}
            addUrl={addUrl}
          />
        }
        leftContent={<div>{leftContent}</div>}
        rightContent={
          <div className="h-full rounded-xl bg-gradient-to-br from-cyan-400/10 to-fuchsia-500/10 p-4 shadow-lg">
            {rightContent ?? <MainMap />}
          </div>
        }
        dialogs={dialogs}
      />
    </div>
  );
}
