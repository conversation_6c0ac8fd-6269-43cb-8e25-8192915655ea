import {
  openConfirmDeleteUnitAtom,
  openUpdateUnitAtom,
} from "@/atoms/app/open-atoms";
import { selectedUnitAtom } from "@/atoms/app/selected-atoms";
import type { Unit } from "@/types/units";
import type { TFunction } from "i18next";
import { Edit, Trash2 } from "lucide-react";
import { createColumns } from "../utils/columns-factory";

export const UnitColumns = (t: TFunction) =>
  createColumns<Unit>({
    t,
    entityKey: "units.list", // 👈 adjust so translations work with nested keys
    fields: [
      { key: "name", title: "common.table.name" },
      {
        key: "driver",
        title: "units.list.table.driver",
        cell: (unit) => unit.driver?.name,
      },
      {
        key: "details.model",
        title: "units.list.table.details.model",
        cell: (unit) => unit.details.model,
      },
      {
        key: "details.year",
        title: "units.list.table.details.year",
        cell: (unit) => unit.details.year,
      },
      {
        key: "details.seats",
        title: "units.list.table.details.seats",
        cell: (unit) => unit.details.seats,
      },
    ],
    actions: [
      {
        icon: <Edit />,
        label: t("common.actions.edit"),
        onClick: (row) => {
          selectedUnitAtom.change("plate_number", row.plate_number);
          openUpdateUnitAtom.open();
        },
      },
      {
        icon: <Trash2 color="red" />,
        label: t("common.actions.delete"),
        onClick: (row) => {
          selectedUnitAtom.change("plate_number", row.plate_number);
          openConfirmDeleteUnitAtom.open();
        },
      },
    ],
  });
