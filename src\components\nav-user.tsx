import {
  Bell,
  ChevronUp,
  CreditCard,
  LogOut,
  Set<PERSON>s,
  <PERSON>rkles,
  User,
} from "lucide-react";

import { authAtom } from "@/atoms/app/auth-atom";
import { openConfirmLogoutDialogAtom } from "@/atoms/app/open-atoms";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar";
import { cn } from "@/lib/utils";
import i18n from "@/localization/i18n";
import { useTranslation } from "react-i18next";
import ConfirmLogoutDialog from "./auth/confirm-logout-dialog";

export function NavUser() {
  const { isMobile } = useSidebar();
  const { user } = authAtom.useValue();
  const { t } = useTranslation();

  const openConfirmLogoutDialog = () => {
    openConfirmLogoutDialogAtom.open();
  };

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu dir={i18n.language === "ar" ? "rtl" : "ltr"}>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className={cn(
                "group h-12 rounded-xl transition-all duration-200",
                "from-brand-secondary/5 to-brand-primary/10 bg-gradient-to-r",
                "hover:from-brand-secondary/10 hover:to-brand-primary/15",
                "data-[state=open]:from-brand-secondary/15 data-[state=open]:to-brand-primary/20",
                "data-[state=open]:ring-brand-secondary/20 data-[state=open]:ring-2",
              )}
            >
              <Avatar className="ring-brand-secondary/20 group-hover:ring-brand-secondary/30 h-8 w-8 ring-2 transition-all">
                <AvatarImage src={user?.image || ""} alt={user?.name} />
                <AvatarFallback className="from-brand-primary to-brand-secondary bg-gradient-to-br font-semibold text-white">
                  {user?.name?.charAt(0) ?? "U"}
                </AvatarFallback>
              </Avatar>
              <div className="grid flex-1 text-start text-sm leading-tight">
                <span className="text-foreground truncate font-semibold">
                  {user?.name || "User"}
                </span>
                <span className="text-muted-foreground truncate text-xs">
                  {user?.email || "<EMAIL>"}
                </span>
              </div>
              <ChevronUp className="text-muted-foreground ml-auto h-4 w-4 transition-transform group-data-[state=open]:rotate-180" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="border-border/50 bg-background/95 ring-border/50 w-64 rounded-xl p-2 shadow-2xl ring-1 backdrop-blur-xl"
            side={isMobile ? "bottom" : "right"}
            align="end"
            sideOffset={8}
          >
            {/* User Info Header */}
            <DropdownMenuLabel className="p-0 font-normal">
              <div className="from-brand-secondary/10 to-brand-primary/5 flex items-center gap-3 rounded-lg bg-gradient-to-r p-3">
                <Avatar className="ring-brand-secondary/30 h-10 w-10 ring-2">
                  <AvatarImage src={user?.image || ""} alt={user?.name} />
                  <AvatarFallback className="from-brand-primary to-brand-secondary bg-gradient-to-br font-semibold text-white">
                    {user?.name?.charAt(0) ?? "U"}
                  </AvatarFallback>
                </Avatar>
                <div className="grid flex-1 text-start">
                  <span className="text-foreground font-semibold">
                    {user?.name || "User"}
                  </span>
                  <span className="text-muted-foreground text-xs">
                    {user?.email || "<EMAIL>"}
                  </span>
                  <div className="mt-1 flex items-center gap-1">
                    <div className="bg-success h-2 w-2 rounded-full"></div>
                    <span className="text-success-dark text-xs">Online</span>
                  </div>
                </div>
              </div>
            </DropdownMenuLabel>

            <DropdownMenuSeparator className="my-2" />

            {/* Pro Upgrade */}
            <DropdownMenuGroup>
              <DropdownMenuItem className="from-warning/10 to-warning-light/10 focus:from-warning/20 focus:to-warning-light/20 rounded-lg bg-gradient-to-r p-3">
                <div className="flex items-center gap-2">
                  <Sparkles className="text-warning h-4 w-4" />
                  <div className="flex-1">
                    <div className="text-warning-dark font-medium">
                      {t("ui.profileMenu.upgradeToPro")}
                    </div>
                    <div className="text-muted-foreground text-xs">
                      Unlock premium features
                    </div>
                  </div>
                </div>
              </DropdownMenuItem>
            </DropdownMenuGroup>

            <DropdownMenuSeparator className="my-2" />

            {/* Menu Items */}
            <DropdownMenuGroup className="space-y-1">
              <DropdownMenuItem className="focus:from-brand-secondary/10 focus:to-brand-primary/10 rounded-lg p-2.5 transition-colors focus:bg-gradient-to-r">
                <User className="text-brand-secondary mr-3 h-4 w-4" />
                <span className="font-medium">
                  {t("ui.profileMenu.account")}
                </span>
              </DropdownMenuItem>
              <DropdownMenuItem className="focus:from-brand-secondary/10 focus:to-brand-primary/10 rounded-lg p-2.5 transition-colors focus:bg-gradient-to-r">
                <Settings className="text-brand-secondary mr-3 h-4 w-4" />
                <span className="font-medium">Settings</span>
              </DropdownMenuItem>
              <DropdownMenuItem className="focus:from-brand-secondary/10 focus:to-brand-primary/10 rounded-lg p-2.5 transition-colors focus:bg-gradient-to-r">
                <CreditCard className="text-brand-secondary mr-3 h-4 w-4" />
                <span className="font-medium">
                  {t("ui.profileMenu.billing")}
                </span>
              </DropdownMenuItem>
              <DropdownMenuItem className="focus:from-brand-secondary/10 focus:to-brand-primary/10 rounded-lg p-2.5 transition-colors focus:bg-gradient-to-r">
                <Bell className="text-brand-secondary mr-3 h-4 w-4" />
                <span className="font-medium">
                  {t("ui.profileMenu.notifications")}
                </span>
              </DropdownMenuItem>
            </DropdownMenuGroup>

            <DropdownMenuSeparator className="my-2" />

            {/* Logout */}
            <DropdownMenuItem
              onClick={openConfirmLogoutDialog}
              className="text-destructive focus:bg-destructive/10 focus:text-destructive rounded-lg p-2.5 transition-colors"
            >
              <LogOut className="mr-3 h-4 w-4" />
              <span className="font-medium">{t("auth.logout")}</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>

      <ConfirmLogoutDialog />
    </SidebarMenu>
  );
}
