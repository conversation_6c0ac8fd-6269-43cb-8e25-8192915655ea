import type {
  CreateUserFormData,
  CreateUserResponse,
  DeleteUserResponse,
  GetAllUsersResponse,
  GetOneUserResponse,
  UpdateUserFormData,
  UpdateUserResponse,
  User,
} from "@/types/users";
import { endpoint } from "@/utils/endpoints";
import { atom } from "@mongez/react-atom";
import { AxiosError } from "axios";
import toast from "react-hot-toast";

interface UsersAtom {
  users: User[];
  oneUser: User | null;
}

interface UsersAtomAction {
  getUsers: () => void;
  createUser: (formData: CreateUserFormData, onSuccess?: () => void) => void;
  getOneUser: (slug: string) => void;
  editUser: (
    slug: string,
    formData: UpdateUserFormData,
    onSuccess?: () => void,
  ) => void;
  deleteUser: (slug: string, onSuccess?: () => void) => void;
}

export const usersAtom = atom<UsersAtom, UsersAtomAction>({
  key: "users-atom",
  default: {
    users: [],
    oneUser: null,
  },

  actions: {
    async getUsers() {
      try {
        const { data } = await endpoint.get<GetAllUsersResponse>("users");
        usersAtom.change("users", data.data);
      } catch (error) {
        if (error instanceof AxiosError) {
          toast.error(error.response?.data.message);
        } else {
          toast.error((error as Error).message);
        }
      }
    },

    async getOneUser(slug: string) {
      try {
        const { data } = await endpoint.get<GetOneUserResponse>(
          `users/${slug}`,
        );
        usersAtom.change("oneUser", data.data);
      } catch (error) {
        if (error instanceof AxiosError) {
          toast.error(error.response?.data.message);
        } else {
          toast.error((error as Error).message);
        }
      }
    },

    async createUser(formData: CreateUserFormData, onSuccess?: () => void) {
      try {
        const { data } = await endpoint.post<CreateUserResponse>(
          "users",
          formData,
        );
        toast.success(data.message);
        onSuccess?.();
        usersAtom.getUsers();
      } catch (error) {
        if (error instanceof AxiosError) {
          toast.error(error.response?.data.message);
        } else {
          toast.error((error as Error).message);
        }
      }
    },

    async editUser(
      slug: string,
      formData: UpdateUserFormData,
      onSuccess?: () => void,
    ) {
      try {
        const { data } = await endpoint.put<UpdateUserResponse>(
          `users/${slug}`,
          formData,
        );
        toast.success(data.message);
        onSuccess?.();
        usersAtom.getUsers();
      } catch (error) {
        if (error instanceof AxiosError) {
          toast.error(error.response?.data.message);
        } else {
          toast.error((error as Error).message);
        }
      }
    },

    async deleteUser(slug: string, onSuccess?: () => void) {
      try {
        const { data } = await endpoint.delete<DeleteUserResponse>(
          `users/${slug}`,
        );
        toast.success(data.data);
        onSuccess?.();
        usersAtom.getUsers();
      } catch (error) {
        if (error instanceof AxiosError) {
          toast.error(error.response?.data.message);
        } else {
          toast.error((error as Error).message);
        }
      }
    },
  },
});
