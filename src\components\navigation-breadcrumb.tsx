import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  B<PERSON><PERSON><PERSON>b<PERSON><PERSON>,
  Bread<PERSON>rumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import React from "react";
import { useTranslation } from "react-i18next";
import { Link, useLocation } from "react-router";

export default function NavigationBreadcrumb() {
  const location = useLocation();
  const { t } = useTranslation();

  const segments = location.pathname.split("/").filter(Boolean);

  // Function to get translated breadcrumb text
  const getBreadcrumbText = (segment: string) => {
    const translationKey = `ui.breadcrumb.${segment}`;
    const translated = t(translationKey);
    // If translation doesn't exist, fallback to formatted segment
    return translated === translationKey
      ? segment.replaceAll("-", " ")
      : translated;
  };

  return (
    <Breadcrumb>
      <BreadcrumbList>
        <BreadcrumbItem>
          <BreadcrumbLink asChild>
            <Link
              to="/"
              className="from-brand-primary to-brand-secondary bg-gradient-to-r bg-clip-text font-bold tracking-wide text-transparent"
            >
              {t("ui.breadcrumb.home")}
            </Link>
          </BreadcrumbLink>
        </BreadcrumbItem>
        {segments.map((segment, index) => {
          const path = "/" + segments.slice(0, index + 1).join("/");
          const isLast = index === segments.length - 1;
          return (
            <React.Fragment key={path}>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                {isLast ? (
                  <span className="from-brand-secondary to-brand-primary bg-gradient-to-r bg-clip-text font-semibold text-transparent capitalize">
                    {getBreadcrumbText(segment)}
                  </span>
                ) : (
                  <BreadcrumbLink asChild>
                    <Link
                      to={path}
                      className="from-brand-primary to-brand-secondary hover:from-brand-primary hover:to-brand-secondary-light bg-gradient-to-r bg-clip-text font-medium text-transparent capitalize"
                    >
                      {getBreadcrumbText(segment)}
                    </Link>
                  </BreadcrumbLink>
                )}
              </BreadcrumbItem>
            </React.Fragment>
          );
        })}
      </BreadcrumbList>
    </Breadcrumb>
  );
}
