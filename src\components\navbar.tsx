import { <PERSON><PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { cn } from "@/lib/utils";
import { Menu, X } from "lucide-react";
import { useState } from "react";
import { NavUserDropdown } from "./nav-user-dropdown";
import NavigationBreadcrumb from "./navigation-breadcrumb";
import FullScreenToggle from "./utils/full-screen-toggle";
import { LanguageToggle } from "./utils/language-toggle";
import { ModeToggle } from "./utils/mode-toggle";
import Notifications from "./utils/notifications";

export default function Navbar() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <>
      <header
        className={cn(
          "sticky top-0 z-50 w-full transition-all duration-300",
          "bg-[var(--navbar-bg)] backdrop-blur-xl",
          "border-b border-[var(--navbar-border)]",
          "supports-[backdrop-filter]:bg-[var(--navbar-bg)]",
        )}
      >
        {/* Modern gradient accent bar */}
        <div
          className="absolute inset-x-0 bottom-0 h-[1px] opacity-60"
          style={{
            background: `linear-gradient(to right, var(--brand-primary), var(--brand-secondary), var(--brand-primary))`,
          }}
        />

        <div className="flex h-16 items-center justify-between px-4 lg:px-6">
          {/* Left section - Sidebar trigger and breadcrumb */}
          <div className="flex min-w-0 flex-1 items-center gap-3">
            <SidebarTrigger
              className={cn(
                "transition-all duration-200 hover:scale-110 active:scale-95",
                "text-[var(--navbar-text)] hover:text-[var(--navbar-accent)]",
              )}
            />

            <Separator
              orientation="vertical"
              className="h-6 w-px opacity-30"
              style={{
                background: `linear-gradient(to bottom, var(--brand-secondary), transparent, var(--brand-primary))`,
              }}
            />

            {/* Breadcrumb - Hidden on mobile, visible on md+ */}
            <div className="hidden min-w-0 flex-1 md:flex">
              <NavigationBreadcrumb />
            </div>
          </div>

          {/* Right section - Controls */}
          <div className="flex items-center gap-2">
            {/* Desktop controls */}
            <div className="hidden items-center gap-1 sm:flex">
              <div
                className={cn(
                  "flex items-center gap-1 rounded-full px-3 py-1.5",
                  "backdrop-blur-sm transition-all duration-200",
                  "hover:scale-[1.02] hover:shadow-lg",
                )}
                style={{
                  background: `linear-gradient(135deg,
                    oklch(from var(--brand-primary) l c h / 0.08),
                    oklch(from var(--brand-secondary) l c h / 0.08))`,
                  border: `1px solid oklch(from var(--brand-primary) l c h / 0.15)`,
                }}
              >
                <LanguageToggle />
                <Notifications />
                <FullScreenToggle />
                <ModeToggle />
                <NavUserDropdown />
              </div>
            </div>

            {/* Mobile menu button */}
            <Button
              variant="ghost"
              size="sm"
              className={cn(
                "touch-target h-9 w-9 p-2 sm:hidden",
                "text-[var(--navbar-text)] hover:text-[var(--navbar-accent)]",
                "hover:bg-[var(--brand-primary)]/10",
                "transition-transform duration-150 active:scale-95",
              )}
              onClick={toggleMobileMenu}
              aria-label="Toggle mobile menu"
              aria-expanded={isMobileMenuOpen}
              aria-controls="mobile-menu"
            >
              {isMobileMenuOpen ? (
                <X className="h-5 w-5" />
              ) : (
                <Menu className="h-5 w-5" />
              )}
            </Button>
          </div>
        </div>

        {/* Mobile breadcrumb - Visible only on mobile when menu is closed */}
        <div
          className={cn(
            "px-4 pb-3 transition-all duration-200 md:hidden",
            isMobileMenuOpen ? "h-0 overflow-hidden opacity-0" : "opacity-100",
          )}
        >
          <NavigationBreadcrumb />
        </div>
      </header>

      {/* Mobile menu overlay */}
      <div
        className={cn(
          "fixed inset-0 z-40 transition-all duration-300 sm:hidden",
          isMobileMenuOpen
            ? "pointer-events-auto opacity-100"
            : "pointer-events-none opacity-0",
        )}
        style={{
          background: isMobileMenuOpen
            ? "oklch(from var(--background) l c h / 0.8)"
            : "transparent",
        }}
        onClick={toggleMobileMenu}
      />

      {/* Mobile menu panel */}
      <div
        id="mobile-menu"
        className={cn(
          "fixed top-16 right-0 z-50 w-80 max-w-[90vw] sm:hidden",
          "mobile-scroll transition-all duration-300 ease-out",
          "border-l shadow-2xl backdrop-blur-xl",
          isMobileMenuOpen
            ? "translate-x-0 opacity-100"
            : "translate-x-full opacity-0",
        )}
        style={{
          background: "var(--navbar-bg)",
          borderColor: "var(--navbar-border)",
        }}
        role="dialog"
        aria-modal="true"
        aria-labelledby="mobile-menu-title"
      >
        <div className="space-y-6 p-6">
          {/* Mobile controls */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span
                id="mobile-menu-title"
                className="text-sm font-medium text-[var(--navbar-text-muted)]"
              >
                Settings
              </span>
            </div>

            <div className="grid grid-cols-2 gap-3">
              <div className="flex flex-col items-center gap-2 rounded-lg p-3 transition-colors hover:bg-[var(--brand-primary)]/5">
                <LanguageToggle />
                <span className="text-xs text-[var(--navbar-text-muted)]">
                  Language
                </span>
              </div>

              <div className="flex flex-col items-center gap-2 rounded-lg p-3 transition-colors hover:bg-[var(--brand-primary)]/5">
                <ModeToggle />
                <span className="text-xs text-[var(--navbar-text-muted)]">
                  Theme
                </span>
              </div>

              <div className="flex flex-col items-center gap-2 rounded-lg p-3 transition-colors hover:bg-[var(--brand-primary)]/5">
                <Notifications />
                <span className="text-xs text-[var(--navbar-text-muted)]">
                  Notifications
                </span>
              </div>

              <div className="flex flex-col items-center gap-2 rounded-lg p-3 transition-colors hover:bg-[var(--brand-primary)]/5">
                <FullScreenToggle />
                <span className="text-xs text-[var(--navbar-text-muted)]">
                  Fullscreen
                </span>
              </div>
            </div>
          </div>

          <Separator className="opacity-30" />

          {/* User section */}
          <div className="space-y-3">
            <span className="text-sm font-medium text-[var(--navbar-text-muted)]">
              Account
            </span>
            <div className="flex items-center justify-center">
              <NavUserDropdown />
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
