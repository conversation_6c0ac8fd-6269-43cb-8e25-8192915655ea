import { <PERSON><PERSON> } from "@/components/ui/button";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { cn } from "@/lib/utils";
import { Menu, X } from "lucide-react";
import { useState } from "react";
import { NavUserDropdown } from "./nav-user-dropdown";
import NavigationBreadcrumb from "./navigation-breadcrumb";
import FullScreenToggle from "./utils/full-screen-toggle";
import { LanguageToggle } from "./utils/language-toggle";
import { ModeToggle } from "./utils/mode-toggle";
import Notifications from "./utils/notifications";

export default function Navbar() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <>
      <header className="sticky top-0 z-50 w-full overflow-hidden rounded-xl">
        {/* Glass morphism background with enhanced blur */}
        <div className="absolute inset-0 bg-gradient-to-r from-slate-900/95 via-slate-800/90 to-slate-900/95 backdrop-blur-2xl" />

        {/* Animated border gradient */}
        <div className="absolute inset-x-0 bottom-0 h-[2px] animate-pulse bg-gradient-to-r from-transparent via-cyan-400/60 to-transparent" />

        {/* Subtle top glow */}
        <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-white/20 to-transparent" />

        <div className="relative">
          <div className="flex h-20 items-center justify-between px-4 lg:px-6">
            {/* Left section - Enhanced sidebar trigger and breadcrumb */}
            <div className="flex min-w-0 flex-1 items-center gap-4">
              {/* Modern sidebar trigger */}
              <div className="flex items-center gap-3">
                <SidebarTrigger
                  className={cn(
                    "h-9 w-9 rounded-xl transition-all duration-300",
                    "bg-gradient-to-br from-white/10 to-white/5",
                    "border border-white/10 backdrop-blur-sm",
                    "hover:scale-110 hover:bg-gradient-to-br hover:from-cyan-400/20 hover:to-blue-500/20",
                    "hover:border-cyan-400/30 hover:shadow-lg hover:shadow-cyan-400/20",
                    "text-slate-300 hover:text-cyan-300 active:scale-95",
                  )}
                />

                {/* Enhanced separator */}
                <div className="h-8 w-px bg-gradient-to-b from-transparent via-slate-400/30 to-transparent" />
              </div>

              {/* Breadcrumb with enhanced styling */}
              <div className="hidden min-w-0 flex-1 md:flex">
                <div className="rounded-lg border border-white/10 bg-white/5 px-3 py-1.5 backdrop-blur-sm">
                  <NavigationBreadcrumb />
                </div>
              </div>
            </div>

            {/* Right section - Redesigned controls */}
            <div className="flex items-center gap-3">
              {/* Desktop controls - Modern card design */}
              <div className="hidden items-center sm:flex">
                <div className="flex items-center gap-2">
                  {/* Control group 1: Settings */}
                  <div className="flex items-center gap-1 rounded-xl bg-white/5 p-1">
                    <LanguageToggle />
                    <ModeToggle />
                  </div>
                  {/* Separator */}
                  <div className="h-6 w-px bg-gradient-to-b from-transparent via-slate-400/30 to-transparent" />
                  {/* Control group 2: Actions */}
                  <div className="flex items-center gap-1 rounded-xl bg-white/5 p-1">
                    <Notifications />
                    <FullScreenToggle />
                  </div>
                  {/* Separator */}
                  <div className="h-6 w-px bg-gradient-to-b from-transparent via-slate-400/30 to-transparent" />
                  {/* User dropdown */}
                  <NavUserDropdown />
                </div>
              </div>

              {/* Enhanced mobile menu button */}
              <Button
                variant="ghost"
                size="sm"
                className={cn(
                  "h-10 w-10 rounded-xl sm:hidden",
                  "bg-gradient-to-br from-white/10 to-white/5",
                  "border border-white/10 backdrop-blur-sm",
                  "hover:bg-gradient-to-br hover:from-cyan-400/20 hover:to-blue-500/20",
                  "hover:border-cyan-400/30 hover:shadow-lg hover:shadow-cyan-400/20",
                  "transition-all duration-300 active:scale-95",
                  "text-slate-300 hover:text-cyan-300",
                )}
                onClick={toggleMobileMenu}
                aria-label="Toggle mobile menu"
                aria-expanded={isMobileMenuOpen}
                aria-controls="mobile-menu"
              >
                <div className="relative">
                  <Menu
                    className={cn(
                      "h-5 w-5 transition-all duration-300",
                      isMobileMenuOpen
                        ? "scale-0 rotate-90"
                        : "scale-100 rotate-0",
                    )}
                  />
                  <X
                    className={cn(
                      "absolute inset-0 h-5 w-5 transition-all duration-300",
                      isMobileMenuOpen
                        ? "scale-100 rotate-0"
                        : "scale-0 -rotate-90",
                    )}
                  />
                </div>
              </Button>
            </div>
          </div>

          {/* Enhanced mobile breadcrumb */}
          <div
            className={cn(
              "px-4 pb-3 transition-all duration-300 md:hidden",
              isMobileMenuOpen
                ? "h-0 overflow-hidden opacity-0"
                : "opacity-100",
            )}
          >
            <div className="rounded-lg border border-white/10 bg-white/5 px-3 py-2 backdrop-blur-sm">
              <NavigationBreadcrumb />
            </div>
          </div>
        </div>
      </header>

      {/* Enhanced mobile menu overlay */}
      <div
        className={cn(
          "fixed inset-0 z-40 transition-all duration-500 sm:hidden",
          isMobileMenuOpen
            ? "pointer-events-auto opacity-100"
            : "pointer-events-none opacity-0",
        )}
        style={{
          background: isMobileMenuOpen ? "rgba(0, 0, 0, 0.6)" : "transparent",
          backdropFilter: isMobileMenuOpen ? "blur(8px)" : "none",
        }}
        onClick={toggleMobileMenu}
      />

      {/* Redesigned mobile menu panel */}
      <div
        id="mobile-menu"
        className={cn(
          "fixed top-20 right-0 z-50 w-80 max-w-[90vw] sm:hidden",
          "transition-all duration-500 ease-out",
          isMobileMenuOpen
            ? "translate-x-0 opacity-100"
            : "translate-x-full opacity-0",
        )}
        role="dialog"
        aria-modal="true"
        aria-labelledby="mobile-menu-title"
      >
        {/* Glass morphism background */}
        <div className="absolute inset-0 rounded-l-2xl bg-gradient-to-br from-slate-900/95 via-slate-800/90 to-slate-900/95 backdrop-blur-2xl" />

        {/* Border and glow effects */}
        <div className="absolute inset-0 rounded-l-2xl border-t border-b border-l border-white/10">
          <div className="absolute inset-0 rounded-l-2xl bg-gradient-to-br from-cyan-400/5 via-transparent to-blue-500/5" />
        </div>

        <div className="relative h-full overflow-y-auto">
          <div className="space-y-6 p-6">
            {/* Header */}
            <div className="flex items-center justify-between">
              <h2
                id="mobile-menu-title"
                className="text-lg font-semibold text-slate-200"
              >
                Quick Settings
              </h2>
              <div className="h-1 w-12 rounded-full bg-gradient-to-r from-cyan-400 to-blue-500" />
            </div>

            {/* Control cards */}
            <div className="space-y-4">
              {/* Settings group */}
              <div className="space-y-3">
                <h3 className="text-sm font-medium text-slate-400">
                  Preferences
                </h3>
                <div className="grid grid-cols-2 gap-3">
                  <div className="group rounded-xl border border-white/10 bg-gradient-to-br from-white/10 to-white/5 p-4 backdrop-blur-sm transition-all duration-300 hover:scale-105 hover:bg-gradient-to-br hover:from-cyan-400/20 hover:to-blue-500/20">
                    <div className="flex flex-col items-center gap-3">
                      <LanguageToggle />
                      <span className="text-xs font-medium text-slate-300 group-hover:text-cyan-300">
                        Language
                      </span>
                    </div>
                  </div>

                  <div className="group rounded-xl border border-white/10 bg-gradient-to-br from-white/10 to-white/5 p-4 backdrop-blur-sm transition-all duration-300 hover:scale-105 hover:bg-gradient-to-br hover:from-cyan-400/20 hover:to-blue-500/20">
                    <div className="flex flex-col items-center gap-3">
                      <ModeToggle />
                      <span className="text-xs font-medium text-slate-300 group-hover:text-cyan-300">
                        Theme
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Actions group */}
              <div className="space-y-3">
                <h3 className="text-sm font-medium text-slate-400">Actions</h3>
                <div className="grid grid-cols-2 gap-3">
                  <div className="group rounded-xl border border-white/10 bg-gradient-to-br from-white/10 to-white/5 p-4 backdrop-blur-sm transition-all duration-300 hover:scale-105 hover:bg-gradient-to-br hover:from-cyan-400/20 hover:to-blue-500/20">
                    <div className="flex flex-col items-center gap-3">
                      <Notifications />
                      <span className="text-xs font-medium text-slate-300 group-hover:text-cyan-300">
                        Notifications
                      </span>
                    </div>
                  </div>

                  <div className="group rounded-xl border border-white/10 bg-gradient-to-br from-white/10 to-white/5 p-4 backdrop-blur-sm transition-all duration-300 hover:scale-105 hover:bg-gradient-to-br hover:from-cyan-400/20 hover:to-blue-500/20">
                    <div className="flex flex-col items-center gap-3">
                      <FullScreenToggle />
                      <span className="text-xs font-medium text-slate-300 group-hover:text-cyan-300">
                        Fullscreen
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Elegant separator */}
            <div className="flex items-center gap-4">
              <div className="h-px flex-1 bg-gradient-to-r from-transparent via-slate-400/30 to-transparent" />
              <div className="h-2 w-2 rounded-full bg-gradient-to-r from-cyan-400 to-blue-500" />
              <div className="h-px flex-1 bg-gradient-to-r from-transparent via-slate-400/30 to-transparent" />
            </div>

            {/* User section */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium text-slate-400">Account</h3>
              <div className="rounded-xl border border-cyan-400/20 bg-gradient-to-br from-cyan-400/10 to-blue-500/10 p-4 backdrop-blur-sm">
                <div className="flex items-center justify-center">
                  <NavUserDropdown />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
