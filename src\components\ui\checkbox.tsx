import * as CheckboxPrimitive from "@radix-ui/react-checkbox";
import { CheckIcon } from "lucide-react";
import * as React from "react";

import { cn } from "@/lib/utils";

function Checkbox({
  className,
  ...props
}: React.ComponentProps<typeof CheckboxPrimitive.Root>) {
  return (
    <CheckboxPrimitive.Root
      data-slot="checkbox"
      className={cn(
        // Base styles
        "peer size-5 shrink-0 rounded-lg border-2 transition-all duration-200 outline-none",
        // Border and background
        "border-border/50 bg-background/50 backdrop-blur-sm",
        "dark:bg-input/20 dark:border-input/30",
        // Checked states with gradient
        "data-[state=checked]:bg-gradient-to-r data-[state=checked]:from-fuchsia-500 data-[state=checked]:to-cyan-400",
        "data-[state=checked]:border-transparent data-[state=checked]:text-white",
        "data-[state=checked]:shadow-lg data-[state=checked]:shadow-fuchsia-500/20",
        // Focus states
        "focus-visible:ring-2 focus-visible:ring-fuchsia-500/30 focus-visible:ring-offset-2",
        "focus-visible:ring-offset-background",
        // Hover states
        "hover:bg-background/70 hover:border-fuchsia-500/30 hover:shadow-md",
        "data-[state=checked]:hover:scale-105 data-[state=checked]:hover:shadow-xl",
        // Invalid states
        "aria-invalid:border-destructive/50 aria-invalid:ring-destructive/20",
        "dark:aria-invalid:ring-destructive/30",
        // Disabled states
        "disabled:bg-muted/30 disabled:cursor-not-allowed disabled:opacity-50",
        className,
      )}
      {...props}
    >
      <CheckboxPrimitive.Indicator
        data-slot="checkbox-indicator"
        className="flex scale-0 items-center justify-center text-current transition-all duration-200 data-[state=checked]:scale-100"
      >
        <CheckIcon className="size-3.5 font-bold" />
      </CheckboxPrimitive.Indicator>
    </CheckboxPrimitive.Root>
  );
}

export { Checkbox };
