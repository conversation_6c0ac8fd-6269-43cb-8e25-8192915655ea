import * as CheckboxPrimitive from "@radix-ui/react-checkbox";
import { CheckIcon } from "lucide-react";
import * as React from "react";

import { cn } from "@/lib/utils";

function Checkbox({
  className,
  ...props
}: React.ComponentProps<typeof CheckboxPrimitive.Root>) {
  return (
    <CheckboxPrimitive.Root
      data-slot="checkbox"
      className={cn(
        // Base styles - simplified
        "peer size-4 shrink-0 rounded border transition-colors duration-200 outline-none",
        // Border and background
        "border-white/30 bg-white/10",
        // Checked states - simple brand color
        "data-[state=checked]:border-[var(--brand-secondary)] data-[state=checked]:bg-[var(--brand-secondary)] data-[state=checked]:text-white",
        // Focus states
        "focus-visible:ring-2 focus-visible:ring-[var(--brand-secondary)]/30 focus-visible:ring-offset-2 focus-visible:ring-offset-slate-900",
        // Hover states
        "hover:border-white/40 hover:bg-white/15",
        "data-[state=checked]:hover:bg-[var(--brand-primary)]",
        // Invalid states
        "aria-invalid:border-red-400 aria-invalid:focus-visible:ring-red-400/30",
        // Disabled states
        "disabled:cursor-not-allowed disabled:opacity-50",
        className,
      )}
      {...props}
    >
      <CheckboxPrimitive.Indicator
        data-slot="checkbox-indicator"
        className="flex scale-0 items-center justify-center text-current transition-all duration-200 data-[state=checked]:scale-100"
      >
        <CheckIcon className="size-3 stroke-[2.5]" />
      </CheckboxPrimitive.Indicator>
    </CheckboxPrimitive.Root>
  );
}

export { Checkbox };
