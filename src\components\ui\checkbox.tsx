import * as CheckboxPrimitive from "@radix-ui/react-checkbox";
import { CheckIcon } from "lucide-react";
import * as React from "react";

import { cn } from "@/lib/utils";

function Checkbox({
  className,
  ...props
}: React.ComponentProps<typeof CheckboxPrimitive.Root>) {
  return (
    <CheckboxPrimitive.Root
      data-slot="checkbox"
      className={cn(
        // Base styles
        "peer size-5 shrink-0 rounded-lg border-2 transition-all duration-300 outline-none",
        // Border and background
        "border-white/20 bg-slate-800/50 backdrop-blur-sm",
        // Checked states with gradient
        "data-[state=checked]:bg-gradient-to-r data-[state=checked]:from-cyan-400 data-[state=checked]:to-blue-500",
        "data-[state=checked]:border-transparent data-[state=checked]:text-white",
        "data-[state=checked]:shadow-lg data-[state=checked]:shadow-cyan-400/20",
        // Focus states
        "focus-visible:ring-2 focus-visible:ring-cyan-400/30 focus-visible:ring-offset-2",
        "focus-visible:ring-offset-slate-900",
        // Hover states
        "hover:border-white/30 hover:bg-slate-800/70 hover:shadow-md",
        "data-[state=checked]:hover:scale-105 data-[state=checked]:hover:shadow-xl data-[state=checked]:hover:shadow-cyan-400/30",
        // Invalid states
        "aria-invalid:border-red-400/50 aria-invalid:ring-red-400/20",
        // Disabled states
        "disabled:cursor-not-allowed disabled:bg-slate-800/30 disabled:opacity-50",
        className,
      )}
      {...props}
    >
      <CheckboxPrimitive.Indicator
        data-slot="checkbox-indicator"
        className="flex scale-0 items-center justify-center text-current transition-all duration-200 data-[state=checked]:scale-100"
      >
        <CheckIcon className="size-3.5 font-bold" />
      </CheckboxPrimitive.Indicator>
    </CheckboxPrimitive.Root>
  );
}

export { Checkbox };
