import * as CheckboxPrimitive from "@radix-ui/react-checkbox";
import { CheckIcon } from "lucide-react";
import * as React from "react";

import { cn } from "@/lib/utils";

function Checkbox({
  className,
  ...props
}: React.ComponentProps<typeof CheckboxPrimitive.Root>) {
  return (
    <CheckboxPrimitive.Root
      data-slot="checkbox"
      className={cn(
        // Base styles
        "peer size-5 shrink-0 rounded-lg border-2 transition-all duration-300 outline-none",
        // Border and background
        "border-white/20 bg-slate-800/50 backdrop-blur-sm",
        // Checked states with gradient - using brand colors
        "data-[state=checked]:border-transparent data-[state=checked]:text-white data-[state=checked]:shadow-lg",
        // Focus states
        "focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-offset-slate-900",
        // Hover states
        "hover:border-white/30 hover:bg-slate-800/70 hover:shadow-md",
        "data-[state=checked]:hover:scale-105 data-[state=checked]:hover:shadow-xl",
        // Invalid states
        "aria-invalid:border-red-400/50 aria-invalid:ring-red-400/20",
        // Disabled states
        "disabled:cursor-not-allowed disabled:bg-slate-800/30 disabled:opacity-50",
        className,
      )}
      onFocus={(e) => {
        e.target.style.setProperty(
          "--tw-ring-color",
          "oklch(from var(--brand-secondary) l c h / 0.3)",
        );
      }}
      ref={(el) => {
        if (el) {
          // Apply brand colors to checked state
          const style = document.createElement("style");
          style.textContent = `
            [data-slot="checkbox"][data-state="checked"] {
              background: linear-gradient(to right, var(--brand-secondary), var(--brand-primary)) !important;
              box-shadow: 0 10px 15px -3px oklch(from var(--brand-secondary) l c h / 0.2) !important;
            }
            [data-slot="checkbox"]:hover[data-state="checked"] {
              box-shadow: 0 25px 25px -5px oklch(from var(--brand-secondary) l c h / 0.3) !important;
            }
          `;
          if (!document.head.querySelector("[data-checkbox-brand-styles]")) {
            style.setAttribute("data-checkbox-brand-styles", "");
            document.head.appendChild(style);
          }
        }
      }}
      {...props}
    >
      <CheckboxPrimitive.Indicator
        data-slot="checkbox-indicator"
        className="flex scale-0 items-center justify-center text-current transition-all duration-200 data-[state=checked]:scale-100"
      >
        <CheckIcon className="size-3.5 font-bold" />
      </CheckboxPrimitive.Indicator>
    </CheckboxPrimitive.Root>
  );
}

export { Checkbox };
