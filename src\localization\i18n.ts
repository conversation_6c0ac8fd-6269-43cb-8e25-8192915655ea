import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import { arTranslations } from "./ar";
import { enTranslations } from "./en";

const resources = {
  en: {
    translation: enTranslations,
  },
  ar: {
    translation: arTranslations,
  },
};

i18n.use(initReactI18next).init({
  resources,
  lng: localStorage.getItem("gps-locale") || "en",
  interpolation: {
    escapeValue: false,
  },
});

export default i18n;
