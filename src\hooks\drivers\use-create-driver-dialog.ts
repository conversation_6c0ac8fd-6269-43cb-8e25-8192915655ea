import { openCreate<PERSON>river<PERSON>tom } from "@/atoms/app/open-atoms";
import { drivers<PERSON>tom } from "@/atoms/entities/drivers-atom";
import type { CreateDriverFormData } from "@/types/drivers";
import { zodResolver } from "@hookform/resolvers/zod";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { z } from "zod";

export default function useCreateDriverDialog() {
  const { t } = useTranslation();

  const FormSchema = z.object({
    name: z.string().nonempty(t("drivers.validation.name")),
    driver_type: z.string().nonempty(t("drivers.validation.driverType")),
    national_id: z.string().nonempty(t("drivers.validation.nationalId")),
    nationality: z.string().nonempty(t("drivers.validation.nationality")),
    identity_number: z
      .string()
      .nonempty(t("drivers.validation.identityNumber")),
    address: z.string().nonempty(t("drivers.validation.address")),
    is_active: z.boolean(),
  });

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      name: "",
      driver_type: "",
      national_id: "",
      nationality: "",
      identity_number: "",
      address: "",
      is_active: false,
    },
  });

  const [isLoading, setIsLoading] = useState(false);
  const isOpened = openCreateDriverAtom.useOpened();

  const closeDialog = () => {
    openCreateDriverAtom.close();
    form.reset();
  };

  async function onSubmit(data: z.infer<typeof FormSchema>) {
    const formData: CreateDriverFormData = {
      name: data.name,
      driver_type: data.driver_type,
      national_id: data.national_id,
      nationality: data.nationality,
      identity_number: data.identity_number,
      address: data.address,
      is_active: data.is_active ? 1 : 0,
    };

    setIsLoading(true);

    await driversAtom.createDriver(formData, () => {
      closeDialog();
    });

    setIsLoading(false);
  }

  const openDialog = () => {
    openCreateDriverAtom.open();
  };

  const driverTypes = [
    { id: "1", label: t("drivers.form.employee") },
    { id: "2", label: t("drivers.form.subcontractor") },
  ];

  return {
    form,
    isLoading,
    isOpened,
    openDialog,
    closeDialog,
    onSubmit,
    t,
    driverTypes,
  };
}
