import { zodResolver } from "@hookform/resolvers/zod";
import { format } from "date-fns";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { useTranslation } from "react-i18next";

export default function useTrackingForm() {
  const { t } = useTranslation();

  // ✅ Validation Schema
  const TrackingFormSchema = z.object({
    vehicleId: z.string().min(1, t("tracking.form.validation.vehicleRequired")),
    startTime: z.date(t("tracking.form.validation.startTimeRequired")),
    endTime: z.date(t("tracking.form.validation.endTimeRequired")),
  });

  type TrackingFormValues = z.infer<typeof TrackingFormSchema>;

  const form = useForm<TrackingFormValues>({
    resolver: zodResolver(TrackingFormSchema),
    defaultValues: {
      vehicleId: "",
      startTime: undefined,
      endTime: undefined,
    },
  });

  function onSubmit(values: TrackingFormValues) {
    console.log({
      vehicleId: values.vehicleId,
      startTime: format(values.startTime!, "yyyy-MM-dd'T'HH:mm:ss"),
      endTime: format(values.endTime!, "yyyy-MM-dd'T'HH:mm:ss"),
    });
  }

  const vehicles = [
    { id: "VHC123", name: "Toyota Corolla - VHC123" },
    { id: "VHC456", name: "Ford Transit - VHC456" },
    { id: "VHC789", name: "Tesla Model 3 - VHC789" },
  ];

  return {
    form,
    onSubmit,
    vehicles,
    t,
  };
}
