import {
  areaShapePoints<PERSON>tom,
  selectedDrawerType<PERSON>tom,
} from "@/atoms/app/selected-atoms";
import { areaGroupsAtom } from "@/atoms/entities/area-groups-atom";
import { areasAtom } from "@/atoms/entities/areas-atom";
import { URLS } from "@/utils/urls";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import toast from "react-hot-toast";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router";
import { z } from "zod";

export default function useAddAreaForm() {
  const { t } = useTranslation();

  const FormSchema = z.object({
    name: z.string().nonempty(t("areas.validation.name_required")),
    code: z.string().nonempty(t("areas.validation.code_required")),
    type: z.number().min(1, t("areas.validation.type_required")),
    color: z.string().nonempty(t("areas.validation.color_required")),
    is_personal: z.boolean(),
    area_group_id: z.number().min(1, t("areas.validation.area_group_required")),
  });

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      name: "",
      code: "",
      type: 0,
      color: "#000000",
      is_personal: false,
      area_group_id: 0,
    },
  });

  const { areaGroups } = areaGroupsAtom.useValue();

  const [isLoading, setIsLoading] = useState(false);

  const navigate = useNavigate();

  useEffect(() => {
    areaGroupsAtom.getAreaGroups();
  }, []);

  const { value: points } = areaShapePointsAtom.useValue();

  async function onSubmit(data: z.infer<typeof FormSchema>) {
    if (!points) {
      toast.error("Please draw an area on the map");
      return;
    }

    setIsLoading(true);

    await areasAtom.createArea(
      {
        ...data,
        points,
      },
      () => {
        navigate(URLS.areas);
        selectedDrawerTypeAtom.change("selectedType", 0);
        areaShapePointsAtom.change("value", null);
      },
    );

    setIsLoading(false);
  }

  useEffect(() => {
    return () => {
      selectedDrawerTypeAtom.reset();
      areaShapePointsAtom.reset();
    };
  }, []);

  const types = [
    { id: 1, label: t("areas.form.circle") },
    { id: 2, label: t("areas.form.polygon") },
  ];

  return {
    form,
    isLoading,
    onSubmit,
    areaGroups,
    t,
    types,
  };
}
