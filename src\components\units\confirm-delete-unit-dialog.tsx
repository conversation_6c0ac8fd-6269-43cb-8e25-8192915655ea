import useConfirmDeleteUnitDialog from "@/hooks/units/use-confirm-delete-unit-dialog";
import i18n from "@/localization/i18n";
import { Button } from "../ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "../ui/dialog";
import { OverlayLoader } from "../utils/overlay-loader";

export default function ConfirmDeleteUnitDialog() {
  const {
    closeDialog,
    deleteUnit,
    isLoading,
    isLoadingFetchUnit,
    isOpened,
    oneUnit,
    t,
  } = useConfirmDeleteUnitDialog();

  return (
    <Dialog open={isOpened} onOpenChange={closeDialog}>
      <DialogContent dir={i18n.language === "ar" ? "rtl" : "ltr"}>
        {isLoadingFetchUnit ? (
          <>
            <DialogHeader>
              <DialogTitle>{t("units.confirmDelete.title")}</DialogTitle>
              <DialogDescription>
                {t("units.confirmDelete.description")}
              </DialogDescription>
            </DialogHeader>

            <OverlayLoader inCenter={false} />
          </>
        ) : (
          <>
            <DialogHeader>
              <DialogTitle>
                {t("units.confirmDelete.title")} - ( {oneUnit?.name} )
              </DialogTitle>
              <DialogDescription>
                {t("units.confirmDelete.description")}
              </DialogDescription>
            </DialogHeader>

            <DialogFooter>
              <DialogClose asChild>
                <Button variant="gradient-outline" size="lg">
                  {t("units.confirmDelete.buttons.cancel")}
                </Button>
              </DialogClose>
              <Button
                variant="destructive-gradient"
                onClick={deleteUnit}
                disabled={isLoading}
                size="lg"
              >
                {isLoading
                  ? t("units.confirmDelete.buttons.deleting")
                  : t("units.confirmDelete.buttons.confirm")}
              </Button>
            </DialogFooter>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
}
