import type { GetModelsResponse, ModelsData } from "@/types/helpers";
import { endpoint } from "@/utils/endpoints";
import { atom } from "@mongez/react-atom";
import { AxiosError } from "axios";
import toast from "react-hot-toast";

interface ModelsAtom {
  models: ModelsData | null;
}

interface ModelsAtomAction {
  getModels: () => void;
}

export const modelsAtom = atom<ModelsAtom, ModelsAtomAction>({
  key: "models-atom",
  default: {
    models: null,
  },

  actions: {
    async getModels() {
      try {
        const { data } = await endpoint.post<GetModelsResponse>(
          "helpers/fetch-data",
          {
            modelNames: ["Protocol", "Driver", "Trailer", "Permission"],
          },
        );
        modelsAtom.change("models", data.data);
      } catch (error) {
        if (error instanceof AxiosError) {
          toast.error(error.response?.data.message);
        } else {
          toast.error((error as Error).message);
        }
      }
    },
  },
});
