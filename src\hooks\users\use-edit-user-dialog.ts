import { openUpdateUser<PERSON>tom } from "@/atoms/app/open-atoms";
import { selectedUser<PERSON>tom } from "@/atoms/app/selected-atoms";
import { usersAtom } from "@/atoms/entities/users-atom";
import type { UpdateUserFormData } from "@/types/users";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { z } from "zod";

export default function useEditUserDialog() {
  const { t } = useTranslation();

  const FormSchema = z.object({
    name: z.string().nonempty(t("users.validation.nameRequired")),
    email: z.string().email(t("users.validation.emailInvalid")),
    phone_code: z.string().nonempty(t("users.validation.phoneCodeRequired")),
    phone: z.string().nonempty(t("users.validation.phoneRequired")),
    lang: z.string().nonempty(t("users.validation.langRequired")),
  });

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      name: "",
      email: "",
      phone_code: "966",
      phone: "",
      lang: "en",
    },
  });

  const { slug } = selectedUserAtom.useValue();
  const { oneUser } = usersAtom.useValue();

  const isOpened = openUpdateUserAtom.useOpened();
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingFetchUser, setIsLoadingFetchUser] = useState(false);

  async function onSubmit(data: z.infer<typeof FormSchema>) {
    const formData: UpdateUserFormData = {
      name: data.name,
      email: data.email,
      phone_code: data.phone_code,
      phone: data.phone,
      lang: data.lang as "en" | "ar",
    };

    setIsLoading(true);

    if (slug) {
      await usersAtom.editUser(slug, formData, () => {
        closeDialog();
      });
    }

    setIsLoading(false);
  }

  const closeDialog = () => {
    openUpdateUserAtom.close();
    selectedUserAtom.reset();
    usersAtom.change("oneUser", null);
  };

  useEffect(() => {
    async function fetchUser() {
      if (slug) {
        setIsLoadingFetchUser(true);
        await usersAtom.getOneUser(slug);
        setIsLoadingFetchUser(false);
      }
    }

    fetchUser();
  }, [slug]);

  useEffect(() => {
    if (oneUser) {
      form.reset({
        name: oneUser.name,
        email: oneUser.email,
        phone_code: oneUser.phone_code,
        phone: oneUser.phone,
        lang: oneUser.lang,
      });
    }
  }, [form, oneUser]);

  return {
    form,
    isOpened,
    isLoading,
    isLoadingFetchUser,
    onSubmit,
    closeDialog,
    t,
  };
}
