import useConfirmDeleteUserDialog from "@/hooks/users/use-confirm-delete-user-dialog";
import i18n from "@/localization/i18n";
import { Button } from "../ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogScrollArea,
  DialogTitle,
} from "../ui/dialog";
import { OverlayLoader } from "../utils/overlay-loader";

export default function ConfirmDeleteUserDialog() {
  const {
    closeDialog,
    deleteUser,
    isLoading,
    isLoadingFetchUser,
    isOpened,
    oneUser,
    t,
  } = useConfirmDeleteUserDialog();

  return (
    <Dialog open={isOpened} onOpenChange={closeDialog}>
      <DialogContent
        dir={i18n.language === "ar" ? "rtl" : "ltr"}
        size="default"
      >
        <DialogHeader>
          <DialogTitle>
            {isLoadingFetchUser
              ? t("users.confirm_delete.deleteUserTitle")
              : `${t("users.confirm_delete.deleteUserTitle")} - (${oneUser?.name})`}
          </DialogTitle>
          <DialogDescription>
            {t("users.confirm_delete.deleteUserDescription")}
          </DialogDescription>
        </DialogHeader>

        <DialogScrollArea>
          {isLoadingFetchUser && <OverlayLoader inCenter={false} />}
        </DialogScrollArea>

        {!isLoadingFetchUser && (
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="gradient-outline" size="lg">
                {t("common.actions.cancel")}
              </Button>
            </DialogClose>
            <Button
              variant="destructive-gradient"
              onClick={deleteUser}
              disabled={isLoading}
              size="lg"
            >
              {isLoading
                ? t("common.loading.deleting")
                : t("common.actions.delete")}
            </Button>
          </DialogFooter>
        )}
      </DialogContent>
    </Dialog>
  );
}
