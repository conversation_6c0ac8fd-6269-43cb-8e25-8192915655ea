@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  /* Brand colors */
  --color-brand-primary: var(--brand-primary);
  --color-brand-primary-light: var(--brand-primary-light);
  --color-brand-primary-dark: var(--brand-primary-dark);
  --color-brand-secondary: var(--brand-secondary);
  --color-brand-secondary-light: var(--brand-secondary-light);
  --color-brand-secondary-dark: var(--brand-secondary-dark);

  /* Semantic colors */
  --color-success: var(--success);
  --color-success-light: var(--success-light);
  --color-success-dark: var(--success-dark);
  --color-warning: var(--warning);
  --color-warning-light: var(--warning-light);
  --color-warning-dark: var(--warning-dark);
  --color-info: var(--info);
  --color-info-light: var(--info-light);
  --color-info-dark: var(--info-dark);
  --color-error: var(--error);
  --color-error-light: var(--error-light);
  --color-error-dark: var(--error-dark);
}

:root {
  --radius: 0.625rem;

  /* Base colors */
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);

  /* Brand colors - Main theme colors */
  --brand-primary: oklch(0.65 0.2 320); /* Fuchsia */
  --brand-primary-light: oklch(0.75 0.18 320);
  --brand-primary-dark: oklch(0.55 0.22 320);
  --brand-secondary: oklch(0.7 0.15 200); /* Cyan */
  --brand-secondary-light: oklch(0.8 0.13 200);
  --brand-secondary-dark: oklch(0.6 0.17 200);

  /* Semantic colors */
  --success: oklch(0.65 0.15 140);
  --success-light: oklch(0.75 0.12 140);
  --success-dark: oklch(0.55 0.18 140);
  --warning: oklch(0.75 0.15 70);
  --warning-light: oklch(0.85 0.12 70);
  --warning-dark: oklch(0.65 0.18 70);
  --info: oklch(0.65 0.15 240);
  --info-light: oklch(0.75 0.12 240);
  --info-dark: oklch(0.55 0.18 240);
  --error: oklch(0.65 0.2 25);
  --error-light: oklch(0.75 0.18 25);
  --error-dark: oklch(0.55 0.22 25);

  /* Navbar specific colors */
  --navbar-bg: oklch(1 0 0 / 0.8);
  --navbar-border: oklch(0.922 0 0 / 0.3);
  --navbar-text: oklch(0.145 0 0);
  --navbar-text-muted: oklch(0.556 0 0);
  --navbar-accent: var(--brand-primary);
  --navbar-accent-secondary: var(--brand-secondary);

  /* Component colors */
  --button-gradient-from: var(--brand-primary);
  --button-gradient-to: var(--brand-secondary);
  --link-color: var(--brand-primary);
  --link-hover: var(--brand-secondary);
  --focus-ring: var(--brand-primary);

  /* Chart colors */
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);

  /* Sidebar colors */
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  /* Base colors */
  --background: oklch(0.25 0 0);
  --foreground: oklch(0.97 0 0);
  --card: oklch(0.3 0 0);
  --card-foreground: oklch(0.97 0 0);
  --popover: oklch(0.3 0 0);
  --popover-foreground: oklch(0.97 0 0);
  --primary: oklch(0.88 0 0);
  --primary-foreground: oklch(0.25 0 0);
  --secondary: oklch(0.38 0 0);
  --secondary-foreground: oklch(0.97 0 0);
  --muted: oklch(0.38 0 0);
  --muted-foreground: oklch(0.75 0 0);
  --accent: oklch(0.38 0 0);
  --accent-foreground: oklch(0.97 0 0);
  --destructive: oklch(0.74 0.191 22.216);
  --border: oklch(1 0 0 / 15%);
  --input: oklch(1 0 0 / 20%);
  --ring: oklch(0.65 0 0);

  /* Brand colors - Adjusted for dark mode */
  --brand-primary: oklch(0.75 0.18 320); /* Lighter fuchsia for dark mode */
  --brand-primary-light: oklch(0.85 0.15 320);
  --brand-primary-dark: oklch(0.65 0.2 320);
  --brand-secondary: oklch(0.8 0.13 200); /* Lighter cyan for dark mode */
  --brand-secondary-light: oklch(0.9 0.1 200);
  --brand-secondary-dark: oklch(0.7 0.15 200);

  /* Semantic colors - Dark mode variants */
  --success: oklch(0.75 0.12 140);
  --success-light: oklch(0.85 0.1 140);
  --success-dark: oklch(0.65 0.15 140);
  --warning: oklch(0.85 0.12 70);
  --warning-light: oklch(0.95 0.1 70);
  --warning-dark: oklch(0.75 0.15 70);
  --info: oklch(0.75 0.12 240);
  --info-light: oklch(0.85 0.1 240);
  --info-dark: oklch(0.65 0.15 240);
  --error: oklch(0.75 0.18 25);
  --error-light: oklch(0.85 0.15 25);
  --error-dark: oklch(0.65 0.2 25);

  /* Navbar specific colors - Dark mode */
  --navbar-bg: oklch(0.25 0 0 / 0.9);
  --navbar-border: oklch(1 0 0 / 0.1);
  --navbar-text: oklch(0.97 0 0);
  --navbar-text-muted: oklch(0.75 0 0);
  --navbar-accent: var(--brand-primary);
  --navbar-accent-secondary: var(--brand-secondary);

  /* Component colors - Dark mode */
  --button-gradient-from: var(--brand-primary);
  --button-gradient-to: var(--brand-secondary);
  --link-color: var(--brand-primary);
  --link-hover: var(--brand-secondary);
  --focus-ring: var(--brand-primary);

  /* Chart colors */
  --chart-1: oklch(0.55 0.243 264.376);
  --chart-2: oklch(0.72 0.17 162.48);
  --chart-3: oklch(0.8 0.188 70.08);
  --chart-4: oklch(0.67 0.265 303.9);
  --chart-5: oklch(0.69 0.246 16.439);

  /* Sidebar colors */
  --sidebar: oklch(0.3 0 0);
  --sidebar-foreground: oklch(0.97 0 0);
  --sidebar-primary: oklch(0.55 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.97 0 0);
  --sidebar-accent: oklch(0.38 0 0);
  --sidebar-accent-foreground: oklch(0.97 0 0);
  --sidebar-border: oklch(1 0 0 / 15%);
  --sidebar-ring: oklch(0.65 0 0);
}

/* .dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
} */

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    font-family: "Tajawal", sans-serif;
  }
}

/* Custom Scrollbar Styles */
@layer utilities {
  /* Global page scrollbar */
  :root {
    scrollbar-width: thin;
    scrollbar-color: oklch(from var(--brand-primary) l c h / 0.3) transparent;
  }

  /* Webkit scrollbar for page */
  ::-webkit-scrollbar {
    width: 12px;
    height: 12px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 10px;
  }

  ::-webkit-scrollbar-thumb {
    background: linear-gradient(
      135deg,
      oklch(from var(--brand-primary) l c h / 0.4) 0%,
      oklch(from var(--brand-secondary) l c h / 0.4) 100%
    );
    border-radius: 10px;
    border: 2px solid transparent;
    background-clip: content-box;
    transition: all 0.2s ease;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(
      135deg,
      oklch(from var(--brand-primary) l c h / 0.6) 0%,
      oklch(from var(--brand-secondary) l c h / 0.6) 100%
    );
    transform: scale(1.1);
  }

  ::-webkit-scrollbar-thumb:active {
    background: linear-gradient(
      135deg,
      oklch(from var(--brand-primary) l c h / 0.8) 0%,
      oklch(from var(--brand-secondary) l c h / 0.8) 100%
    );
  }

  ::-webkit-scrollbar-corner {
    background: transparent;
  }

  /* Dark mode scrollbar adjustments */
  .dark ::-webkit-scrollbar-thumb {
    background: linear-gradient(
      135deg,
      rgba(217, 70, 239, 0.5) 0%,
      rgba(6, 182, 212, 0.5) 100%
    );
  }

  .dark ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(
      135deg,
      rgba(217, 70, 239, 0.7) 0%,
      rgba(6, 182, 212, 0.7) 100%
    );
  }

  /* Dialog scroll area specific styles */
  .dialog-scroll {
    scrollbar-width: thin;
    scrollbar-color: rgba(217, 70, 239, 0.4) transparent;
  }

  .dialog-scroll::-webkit-scrollbar {
    width: 8px;
  }

  .dialog-scroll::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    margin: 4px;
  }

  .dialog-scroll::-webkit-scrollbar-thumb {
    background: linear-gradient(
      180deg,
      rgba(217, 70, 239, 0.5) 0%,
      rgba(6, 182, 212, 0.5) 100%
    );
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
  }

  .dialog-scroll::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(
      180deg,
      rgba(217, 70, 239, 0.7) 0%,
      rgba(6, 182, 212, 0.7) 100%
    );
    transform: scaleX(1.2);
    box-shadow: 0 0 10px rgba(217, 70, 239, 0.3);
  }

  /* Smooth scrolling for all elements */
  * {
    scroll-behavior: smooth;
  }

  /* Custom scrollbar animations */
  @keyframes scrollbar-glow {
    0% {
      box-shadow: 0 0 5px rgba(217, 70, 239, 0.3);
    }
    50% {
      box-shadow: 0 0 15px rgba(6, 182, 212, 0.5);
    }
    100% {
      box-shadow: 0 0 5px rgba(217, 70, 239, 0.3);
    }
  }

  .dialog-scroll::-webkit-scrollbar-thumb:active {
    animation: scrollbar-glow 0.6s ease-in-out;
  }

  /* Enhanced scroll utility classes */
  .scroll-gradient {
    scrollbar-width: thin;
    scrollbar-color: rgba(217, 70, 239, 0.4) transparent;
  }

  .scroll-gradient::-webkit-scrollbar {
    width: 10px;
    height: 10px;
  }

  .scroll-gradient::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.03);
    border-radius: 8px;
    margin: 2px;
  }

  .scroll-gradient::-webkit-scrollbar-thumb {
    background: linear-gradient(
      45deg,
      rgba(217, 70, 239, 0.4) 0%,
      rgba(6, 182, 212, 0.4) 100%
    );
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .scroll-gradient::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(
      45deg,
      rgba(217, 70, 239, 0.6) 0%,
      rgba(6, 182, 212, 0.6) 100%
    );
    transform: scale(1.1);
    box-shadow:
      0 0 10px rgba(217, 70, 239, 0.3),
      0 0 20px rgba(6, 182, 212, 0.2);
  }

  /* Thin scroll variant */
  .scroll-thin {
    scrollbar-width: thin;
    scrollbar-color: rgba(217, 70, 239, 0.3) transparent;
  }

  .scroll-thin::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .scroll-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scroll-thin::-webkit-scrollbar-thumb {
    background: linear-gradient(
      180deg,
      rgba(217, 70, 239, 0.5) 0%,
      rgba(6, 182, 212, 0.5) 100%
    );
    border-radius: 6px;
    transition: all 0.2s ease;
  }

  .scroll-thin::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(
      180deg,
      rgba(217, 70, 239, 0.7) 0%,
      rgba(6, 182, 212, 0.7) 100%
    );
  }

  /* Hide scrollbar but keep functionality */
  .scroll-hidden {
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .scroll-hidden::-webkit-scrollbar {
    display: none;
  }

  /* Modern form input enhancements */
  .form-input-modern {
    @apply relative overflow-hidden;
  }

  .form-input-modern::before {
    content: "";
    @apply absolute inset-0 rounded-lg bg-gradient-to-r from-fuchsia-500/5 to-cyan-400/5 opacity-0 transition-opacity duration-200;
  }

  .form-input-modern:focus-within::before {
    @apply opacity-100;
  }

  /* Button hover glow effect */
  .btn-glow {
    @apply relative overflow-hidden;
  }

  .btn-glow::before {
    content: "";
    @apply absolute inset-0 bg-gradient-to-r from-fuchsia-500/20 to-cyan-400/20 opacity-0 transition-opacity duration-300;
  }

  .btn-glow:hover::before {
    @apply opacity-100;
  }

  /* Table row hover animation */
  .table-row-modern {
    @apply relative overflow-hidden;
  }

  .table-row-modern::before {
    content: "";
    @apply absolute inset-0 translate-x-[-100%] bg-gradient-to-r from-fuchsia-500/3 to-cyan-400/3 opacity-0 transition-all duration-200;
  }

  .table-row-modern:hover::before {
    @apply translate-x-0 opacity-100;
  }

  /* Enhanced focus ring for accessibility */
  .focus-ring-modern {
    @apply focus-visible:ring-offset-background focus-visible:ring-2 focus-visible:ring-offset-2;
    --tw-ring-color: oklch(from var(--focus-ring) l c h / 0.3);
  }

  /* Gradient text utility */
  .text-gradient {
    background: linear-gradient(
      to right,
      var(--brand-primary),
      var(--brand-secondary)
    );
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
  }

  /* Modern card shadow */
  .card-shadow-modern {
    box-shadow:
      0 1px 3px 0 rgba(217, 70, 239, 0.1),
      0 1px 2px 0 rgba(6, 182, 212, 0.06),
      0 0 0 1px rgba(255, 255, 255, 0.05);
  }

  .card-shadow-modern:hover {
    box-shadow:
      0 4px 6px -1px rgba(217, 70, 239, 0.15),
      0 2px 4px -1px rgba(6, 182, 212, 0.1),
      0 0 0 1px rgba(255, 255, 255, 0.1);
  }

  /* Input field animations */
  @keyframes input-focus {
    0% {
      box-shadow: 0 0 0 0 rgba(217, 70, 239, 0.3);
    }
    70% {
      box-shadow: 0 0 0 4px rgba(217, 70, 239, 0.1);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(217, 70, 239, 0);
    }
  }

  .input-focus-animation:focus {
    animation: input-focus 0.6s ease-out;
  }

  /* Button press animation */
  @keyframes button-press {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(0.98);
    }
    100% {
      transform: scale(1);
    }
  }

  .btn-press:active {
    animation: button-press 0.15s ease-out;
  }

  /* Mobile-specific enhancements */
  @media (max-width: 640px) {
    /* Touch-friendly tap targets */
    .touch-target {
      min-height: 44px;
      min-width: 44px;
    }

    /* Mobile menu animations */
    .mobile-menu-enter {
      transform: translateX(100%);
      opacity: 0;
    }

    .mobile-menu-enter-active {
      transform: translateX(0);
      opacity: 1;
      transition: all 300ms ease-out;
    }

    .mobile-menu-exit {
      transform: translateX(0);
      opacity: 1;
    }

    .mobile-menu-exit-active {
      transform: translateX(100%);
      opacity: 0;
      transition: all 300ms ease-in;
    }

    /* Mobile-optimized scrollbars */
    .mobile-scroll::-webkit-scrollbar {
      width: 4px;
    }

    .mobile-scroll::-webkit-scrollbar-thumb {
      background: linear-gradient(
        180deg,
        oklch(from var(--brand-primary) l c h / 0.6) 0%,
        oklch(from var(--brand-secondary) l c h / 0.6) 100%
      );
      border-radius: 4px;
    }

    /* Reduce motion for mobile users who prefer it */
    @media (prefers-reduced-motion: reduce) {
      * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
      }
    }
  }

  /* Tablet-specific adjustments */
  @media (min-width: 641px) and (max-width: 1024px) {
    .tablet-optimized {
      padding: 1rem;
    }
  }

  /* High DPI displays */
  @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .high-dpi-shadow {
      box-shadow:
        0 2px 4px -1px oklch(from var(--brand-primary) l c h / 0.1),
        0 1px 2px -1px oklch(from var(--brand-secondary) l c h / 0.06);
    }
  }
}

/* @import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  --radius: 0.625rem;

  --background: oklch(0.98 0.02 240);
  --foreground: oklch(0.2 0.05 240);

  --card: oklch(1 0 0);
  --card-foreground: oklch(0.2 0.05 240);

  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.2 0.05 240);

  --primary: oklch(0.55 0.18 240);
  --primary-foreground: oklch(0.98 0.02 240);

  --secondary: oklch(0.85 0.05 240);
  --secondary-foreground: oklch(0.2 0.05 240);

  --muted: oklch(0.92 0.03 240);
  --muted-foreground: oklch(0.4 0.05 240);

  --accent: oklch(0.88 0.05 240);
  --accent-foreground: oklch(0.2 0.05 240);

  --destructive: oklch(0.6 0.18 25);
  --border: oklch(0.9 0.02 240);
  --input: oklch(0.9 0.02 240);
  --ring: oklch(0.55 0.18 240);

  --chart-1: oklch(0.55 0.18 240);
  --chart-2: oklch(0.62 0.16 220);
  --chart-3: oklch(0.45 0.18 250);
  --chart-4: oklch(0.7 0.12 210);
  --chart-5: oklch(0.6 0.14 260);

  --sidebar: oklch(0.98 0.02 240);
  --sidebar-foreground: oklch(0.2 0.05 240);
  --sidebar-primary: oklch(0.55 0.18 240);
  --sidebar-primary-foreground: oklch(0.98 0.02 240);
  --sidebar-accent: oklch(0.88 0.05 240);
  --sidebar-accent-foreground: oklch(0.2 0.05 240);
  --sidebar-border: oklch(0.9 0.02 240);
  --sidebar-ring: oklch(0.55 0.18 240);
}

.dark {
  --background: oklch(0.18 0.03 240);
  --foreground: oklch(0.95 0.02 240);

  --card: oklch(0.22 0.04 240);
  --card-foreground: oklch(0.95 0.02 240);

  --popover: oklch(0.22 0.04 240);
  --popover-foreground: oklch(0.95 0.02 240);

  --primary: oklch(0.7 0.14 240);
  --primary-foreground: oklch(0.15 0.03 240);

  --secondary: oklch(0.28 0.05 240);
  --secondary-foreground: oklch(0.95 0.02 240);

  --muted: oklch(0.28 0.05 240);
  --muted-foreground: oklch(0.7 0.02 240);

  --accent: oklch(0.3 0.05 240);
  --accent-foreground: oklch(0.95 0.02 240);

  --destructive: oklch(0.65 0.18 25);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.6 0.14 240);

  --chart-1: oklch(0.65 0.14 240);
  --chart-2: oklch(0.55 0.16 220);
  --chart-3: oklch(0.75 0.12 260);
  --chart-4: oklch(0.6 0.14 210);
  --chart-5: oklch(0.7 0.14 250);

  --sidebar: oklch(0.22 0.04 240);
  --sidebar-foreground: oklch(0.95 0.02 240);
  --sidebar-primary: oklch(0.65 0.14 240);
  --sidebar-primary-foreground: oklch(0.15 0.03 240);
  --sidebar-accent: oklch(0.3 0.05 240);
  --sidebar-accent-foreground: oklch(0.95 0.02 240);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.6 0.14 240);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
} */
