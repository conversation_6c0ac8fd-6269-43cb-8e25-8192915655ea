import useConfirmDeleteAreaDialog from "@/hooks/areas/use-confirm-delete-area-dialog";
import i18n from "@/localization/i18n";
import { Button } from "../ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "../ui/dialog";
import { OverlayLoader } from "../utils/overlay-loader";

export default function ConfirmDeleteAreaDialog() {
  const {
    area,
    closeDialog,
    deleteArea,
    isLoading,
    isLoadingFetchArea,
    isOpened,
    t,
  } = useConfirmDeleteAreaDialog();

  return (
    <Dialog open={isOpened} onOpenChange={closeDialog}>
      <DialogContent dir={i18n.language === "ar" ? "rtl" : "ltr"}>
        {isLoadingFetchArea ? (
          <>
            <DialogHeader>
              <DialogTitle>{t("areas.confirm_delete.title")}</DialogTitle>
              <DialogDescription>
                {t("areas.confirm_delete.description")}
              </DialogDescription>
            </DialogHeader>

            <OverlayLoader inCenter={false} />
          </>
        ) : (
          <>
            <DialogHeader>
              <DialogTitle>
                {t("areas.confirm_delete.title")} ( {area?.name} )
              </DialogTitle>
              <DialogDescription>
                {t("areas.confirm_delete.description")}
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <DialogClose asChild>
                <Button
                  variant="gradient-outline"
                  onClick={closeDialog}
                  size="lg"
                >
                  {t("common.actions.cancel")}
                </Button>
              </DialogClose>
              <Button
                variant="destructive-gradient"
                onClick={deleteArea}
                disabled={isLoading}
                size="lg"
              >
                {isLoading
                  ? t("common.loading.deleting")
                  : t("common.actions.delete")}
              </Button>
            </DialogFooter>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
}
