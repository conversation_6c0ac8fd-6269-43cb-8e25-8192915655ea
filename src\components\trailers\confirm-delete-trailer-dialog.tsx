import useConfirmDeleteTrailerDialog from "@/hooks/trilers/use-confirm-delete-trailer-dialog";
import i18n from "@/localization/i18n";
import { Button } from "../ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "../ui/dialog";
import { OverlayLoader } from "../utils/overlay-loader";

export default function ConfirmDeleteTrailerDialog() {
  const {
    closeDialog,
    deleteTrailer,
    isLoading,
    isLoadingFetchTrailer,
    isOpened,
    oneTrailer,
    t,
  } = useConfirmDeleteTrailerDialog();

  return (
    <Dialog open={isOpened} onOpenChange={closeDialog}>
      <DialogContent dir={i18n.language === "ar" ? "rtl" : "ltr"}>
        {isLoadingFetchTrailer ? (
          <>
            <DialogHeader>
              <DialogTitle>
                {t("trailers.confirm_delete.deleteTrailerTitle")}
              </DialogTitle>
              <DialogDescription>
                {t("trailers.confirm_delete.deleteTrailerDescription")}
              </DialogDescription>
            </DialogHeader>

            <OverlayLoader inCenter={false} />
          </>
        ) : (
          <>
            <DialogHeader>
              <DialogTitle>
                {t("trailers.confirm_delete.deleteTrailerTitle")} - ({" "}
                {oneTrailer?.name} )
              </DialogTitle>
              <DialogDescription>
                {t("trailers.confirm_delete.deleteTrailerDescription")}
              </DialogDescription>
            </DialogHeader>

            <DialogFooter>
              <DialogClose asChild>
                <Button variant="gradient-outline" size="lg">
                  {t("trailers.confirm_delete.cancel")}
                </Button>
              </DialogClose>
              <Button
                variant="destructive-gradient"
                onClick={deleteTrailer}
                disabled={isLoading}
                size="lg"
              >
                {isLoading
                  ? t("trailers.confirm_delete.deleting")
                  : t("trailers.confirm_delete.delete")}
              </Button>
            </DialogFooter>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
}
