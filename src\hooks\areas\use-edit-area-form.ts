import {
  areaShapePoints<PERSON><PERSON>,
  selectedDrawerType<PERSON>tom,
} from "@/atoms/app/selected-atoms";
import { areaGroupsAtom } from "@/atoms/entities/area-groups-atom";
import { areasAtom } from "@/atoms/entities/areas-atom";
import { URLS } from "@/utils/urls";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams } from "react-router";
import { z } from "zod";

export default function useEditAreaForm() {
  const { t } = useTranslation();

  const FormSchema = z.object({
    name: z.string().nonempty(t("areas.validation.name_required")),
    code: z.string().nonempty(t("areas.validation.code_required")),
    type: z.number().min(1, t("areas.validation.type_required")),
    color: z.string().nonempty(t("areas.validation.color_required")),
    is_personal: z.boolean(),
    area_group_id: z.number().min(1, t("areas.validation.area_group_required")),
  });

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      name: "",
      code: "",
      type: 0,
      color: "#000000",
      is_personal: false,
      area_group_id: 0,
    },
  });

  const navigate = useNavigate();
  const { slug } = useParams<{ slug: string }>();

  const { area } = areasAtom.useValue();
  const { areaGroups } = areaGroupsAtom.useValue();
  const { value: points } = areaShapePointsAtom.useValue();

  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingFetchArea, setIsLoadingFetchArea] = useState(false);

  useEffect(() => {
    async function fetchArea() {
      if (slug) {
        setIsLoadingFetchArea(true);
        await areasAtom.getOneArea(slug);
        setIsLoadingFetchArea(false);
      }
    }
    fetchArea();
  }, [slug]);

  useEffect(() => {
    async function setFormValues() {
      setIsLoadingFetchArea(true);
      await areaGroupsAtom.getAreaGroups();
      setIsLoadingFetchArea(false);

      if (area) {
        form.reset({
          name: area.name,
          code: area.code,
          type: area.type,
          color: area.color,
          is_personal: !!area.is_personal,
          area_group_id: area.area_group_id,
        });
      }
    }

    setFormValues();
  }, [area, form]);

  async function onSubmit(data: z.infer<typeof FormSchema>) {
    setIsLoading(true);

    if (slug) {
      await areasAtom.editArea(
        slug,
        points ? { ...data, points } : { ...data },
        () => {
          navigate(URLS.areas);
          selectedDrawerTypeAtom.change("selectedType", 0);
          areaShapePointsAtom.change("value", null);
        },
      );
    }

    setIsLoading(false);
  }

  useEffect(() => {
    return () => {
      selectedDrawerTypeAtom.reset();
      areaShapePointsAtom.reset();
    };
  }, []);

  const types = [
    { id: 1, label: t("areas.form.circle") },
    { id: 2, label: t("areas.form.polygon") },
  ];

  return {
    form,
    isLoading,
    isLoadingFetchArea,
    onSubmit,
    areaGroups,
    t,
    types,
  };
}
