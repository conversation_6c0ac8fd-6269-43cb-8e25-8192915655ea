import * as SwitchPrimitive from "@radix-ui/react-switch";
import * as React from "react";

import { cn } from "@/lib/utils";

function Switch({
  className,
  ...props
}: React.ComponentProps<typeof SwitchPrimitive.Root>) {
  return (
    <SwitchPrimitive.Root
      dir="ltr"
      data-slot="switch"
      className={cn(
        // Base styles
        "peer inline-flex h-6 w-11 shrink-0 items-center rounded-full border-2 transition-all duration-300 outline-none",
        // Unchecked state
        "data-[state=unchecked]:border-white/20 data-[state=unchecked]:bg-slate-800/50",
        // Checked state with gradient
        "data-[state=checked]:bg-gradient-to-r data-[state=checked]:from-cyan-400 data-[state=checked]:to-blue-500",
        "data-[state=checked]:border-transparent data-[state=checked]:shadow-lg data-[state=checked]:shadow-cyan-400/20",
        // Focus states
        "focus-visible:ring-2 focus-visible:ring-cyan-400/30 focus-visible:ring-offset-2",
        "focus-visible:ring-offset-slate-900",
        // Hover states
        "hover:data-[state=unchecked]:border-white/30 hover:data-[state=unchecked]:bg-slate-800/70",
        "hover:data-[state=checked]:scale-105 hover:data-[state=checked]:shadow-xl hover:data-[state=checked]:shadow-cyan-400/30",
        // Disabled states
        "disabled:cursor-not-allowed disabled:opacity-50",
        className,
      )}
      {...props}
    >
      <SwitchPrimitive.Thumb
        data-slot="switch-thumb"
        className={cn(
          // Base thumb styles
          "pointer-events-none block size-5 rounded-full transition-all duration-300",
          // Unchecked thumb
          "data-[state=unchecked]:translate-x-0 data-[state=unchecked]:bg-slate-300 data-[state=unchecked]:shadow-md",
          // Checked thumb
          "data-[state=checked]:translate-x-5 data-[state=checked]:bg-white",
          "data-[state=checked]:scale-110 data-[state=checked]:shadow-lg",
        )}
      />
    </SwitchPrimitive.Root>
  );
}

export { Switch };
