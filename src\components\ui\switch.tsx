import * as SwitchPrimitive from "@radix-ui/react-switch";
import * as React from "react";

import { cn } from "@/lib/utils";

function Switch({
  className,
  ...props
}: React.ComponentProps<typeof SwitchPrimitive.Root>) {
  return (
    <SwitchPrimitive.Root
      dir="ltr"
      data-slot="switch"
      className={cn(
        // Base styles - simplified
        "peer inline-flex h-5 w-9 shrink-0 items-center rounded-full border transition-colors duration-200 outline-none",
        // Unchecked state
        "data-[state=unchecked]:border-white/30 data-[state=unchecked]:bg-white/10",
        // Checked state - simple brand color
        "data-[state=checked]:border-[var(--brand-secondary)] data-[state=checked]:bg-[var(--brand-secondary)]",
        // Focus states
        "focus-visible:ring-2 focus-visible:ring-[var(--brand-secondary)]/30 focus-visible:ring-offset-2 focus-visible:ring-offset-slate-900",
        // Hover states
        "hover:data-[state=unchecked]:border-white/40 hover:data-[state=unchecked]:bg-white/15",
        "hover:data-[state=checked]:bg-[var(--brand-primary)]",
        // Disabled states
        "disabled:cursor-not-allowed disabled:opacity-50",
        className,
      )}
      {...props}
    >
      <SwitchPrimitive.Thumb
        data-slot="switch-thumb"
        className={cn(
          // Base thumb styles - simplified
          "pointer-events-none block size-4 rounded-full bg-white shadow-sm transition-transform duration-200",
          // Positioning
          "data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0",
        )}
      />
    </SwitchPrimitive.Root>
  );
}

export { Switch };
