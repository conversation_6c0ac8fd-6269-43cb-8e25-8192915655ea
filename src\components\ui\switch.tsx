import * as SwitchPrimitive from "@radix-ui/react-switch";
import * as React from "react";

import { cn } from "@/lib/utils";

function Switch({
  className,
  ...props
}: React.ComponentProps<typeof SwitchPrimitive.Root>) {
  return (
    <SwitchPrimitive.Root
      dir="ltr"
      data-slot="switch"
      className={cn(
        // Base styles
        "peer inline-flex h-6 w-11 shrink-0 items-center rounded-full border-2 transition-all duration-300 outline-none",
        // Unchecked state
        "data-[state=unchecked]:border-white/20 data-[state=unchecked]:bg-slate-800/50",
        // Checked state with gradient - using brand colors
        "data-[state=checked]:border-transparent data-[state=checked]:shadow-lg",
        // Focus states
        "focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-offset-slate-900",
        // Hover states
        "hover:data-[state=unchecked]:border-white/30 hover:data-[state=unchecked]:bg-slate-800/70",
        "hover:data-[state=checked]:scale-105 hover:data-[state=checked]:shadow-xl",
        // Disabled states
        "disabled:cursor-not-allowed disabled:opacity-50",
        className,
      )}
      style={
        {
          "--checked-bg":
            "linear-gradient(to right, var(--brand-secondary), var(--brand-primary))",
          "--checked-shadow": "oklch(from var(--brand-secondary) l c h / 0.2)",
          "--focus-ring": "oklch(from var(--brand-secondary) l c h / 0.3)",
          "--hover-shadow": "oklch(from var(--brand-secondary) l c h / 0.3)",
        } as React.CSSProperties
      }
      onFocus={(e) => {
        e.target.style.setProperty(
          "--tw-ring-color",
          "oklch(from var(--brand-secondary) l c h / 0.3)",
        );
      }}
      ref={(el) => {
        if (el) {
          // Apply brand colors to checked state
          const style = document.createElement("style");
          style.textContent = `
            [data-slot="switch"][data-state="checked"] {
              background: linear-gradient(to right, var(--brand-secondary), var(--brand-primary)) !important;
              box-shadow: 0 10px 15px -3px oklch(from var(--brand-secondary) l c h / 0.2) !important;
            }
            [data-slot="switch"]:hover[data-state="checked"] {
              box-shadow: 0 25px 25px -5px oklch(from var(--brand-secondary) l c h / 0.3) !important;
            }
          `;
          if (!document.head.querySelector("[data-switch-brand-styles]")) {
            style.setAttribute("data-switch-brand-styles", "");
            document.head.appendChild(style);
          }
        }
      }}
      {...props}
    >
      <SwitchPrimitive.Thumb
        data-slot="switch-thumb"
        className={cn(
          // Base thumb styles
          "pointer-events-none block size-5 rounded-full transition-all duration-300",
          // Unchecked thumb
          "data-[state=unchecked]:translate-x-0 data-[state=unchecked]:bg-slate-300 data-[state=unchecked]:shadow-md",
          // Checked thumb
          "data-[state=checked]:translate-x-5 data-[state=checked]:bg-white",
          "data-[state=checked]:scale-110 data-[state=checked]:shadow-lg",
        )}
      />
    </SwitchPrimitive.Root>
  );
}

export { Switch };
