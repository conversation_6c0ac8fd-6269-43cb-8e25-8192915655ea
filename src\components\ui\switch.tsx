import * as SwitchPrimitive from "@radix-ui/react-switch";
import * as React from "react";

import { cn } from "@/lib/utils";

function Switch({
  className,
  ...props
}: React.ComponentProps<typeof SwitchPrimitive.Root>) {
  return (
    <SwitchPrimitive.Root
      dir="ltr"
      data-slot="switch"
      className={cn(
        // Base styles
        "peer inline-flex h-6 w-11 shrink-0 items-center rounded-full border-2 transition-all duration-200 outline-none",
        // Unchecked state
        "data-[state=unchecked]:bg-input/50 data-[state=unchecked]:border-border/50",
        "dark:data-[state=unchecked]:bg-input/30 dark:data-[state=unchecked]:border-input/30",
        // Checked state with gradient
        "data-[state=checked]:bg-gradient-to-r data-[state=checked]:from-fuchsia-500 data-[state=checked]:to-cyan-400",
        "data-[state=checked]:border-transparent data-[state=checked]:shadow-lg data-[state=checked]:shadow-fuchsia-500/20",
        // Focus states
        "focus-visible:ring-2 focus-visible:ring-fuchsia-500/30 focus-visible:ring-offset-2",
        "focus-visible:ring-offset-background",
        // Hover states
        "hover:data-[state=unchecked]:bg-input/70 hover:data-[state=unchecked]:border-border",
        "hover:data-[state=checked]:scale-105 hover:data-[state=checked]:shadow-xl",
        // Disabled states
        "disabled:cursor-not-allowed disabled:opacity-50",
        className,
      )}
      {...props}
    >
      <SwitchPrimitive.Thumb
        data-slot="switch-thumb"
        className={cn(
          // Base thumb styles
          "pointer-events-none block size-5 rounded-full transition-all duration-200",
          // Unchecked thumb
          "data-[state=unchecked]:bg-background data-[state=unchecked]:translate-x-0",
          "dark:data-[state=unchecked]:bg-foreground/90 data-[state=unchecked]:shadow-md",
          // Checked thumb
          "data-[state=checked]:translate-x-5 data-[state=checked]:bg-white",
          "data-[state=checked]:scale-110 data-[state=checked]:shadow-lg",
        )}
      />
    </SwitchPrimitive.Root>
  );
}

export { Switch };
