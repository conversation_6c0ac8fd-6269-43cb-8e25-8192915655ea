import type { Points } from "@/types/areas";
import { atom } from "@mongez/react-atom";

/**
 * users
 */
export const selectedUserAtom = atom<{ slug: string | null }>({
  key: "selected-user-atom",
  default: {
    slug: null,
  },
});

/**
 * units
 */
export const selectedUnitAtom = atom<{ plate_number: string | null }>({
  key: "selected-units-atom",
  default: {
    plate_number: null,
  },
});

/**
 * trailers
 */
export const selectedTrailerAtom = atom<{ slug: string | null }>({
  key: "selected-trailer-atom",
  default: {
    slug: null,
  },
});

/**
 * drivers
 */
export const selectedDriverAtom = atom<{ slug: string | null }>({
  key: "selected-driver-atom",
  default: {
    slug: null,
  },
});

/**
 * areas
 */
export const selectedDrawerTypeAtom = atom<{ selectedType: 0 | 1 | 2 }>({
  key: "selected-drawer-type-atom",
  default: {
    /***
     * 0 : not selected
     * 1 : circle
     * 2 : polygon
     */
    selectedType: 0,
  },
});

export const selectedAreaToDeleteAtom = atom<{ slug: string | null }>({
  key: "selected-area-to-delete-atom",
  default: {
    slug: null,
  },
});

export const areaShapePointsAtom = atom<{ value: Points | null }>({
  key: "area-shape-points-atom",
  default: {
    value: null,
  },
});
