import type {
  CreateTrailerFormData,
  CreateTrailerResponse,
  DeleteTrailerResponse,
  GetAllTrailersResponse,
  GetOneTrailerResponse,
  Trailer,
  UpdateTrailerResponse,
} from "@/types/trailers";
import { endpoint } from "@/utils/endpoints";
import { atom } from "@mongez/react-atom";
import { AxiosError } from "axios";
import toast from "react-hot-toast";

interface TrailersAtom {
  trailers: Trailer[];
  oneTrailer: Trailer | null;
}

interface TrailersAtomAction {
  getTrailers: () => void;
  createTrailer: (
    formData: CreateTrailerFormData,
    onSuccess?: () => void,
  ) => void;
  getOneTrailer: (slug: string) => void;
  editTrailer: (
    slug: string,
    formData: CreateTrailerFormData,
    onSuccess?: () => void,
  ) => void;
  deleteTrailer: (slug: string, onSuccess?: () => void) => void;
}

export const trailresAtom = atom<TrailersAtom, TrailersAtomAction>({
  key: "trailers-atom",
  default: {
    trailers: [],
  },

  actions: {
    async getTrailers() {
      try {
        const { data } = await endpoint.get<GetAllTrailersResponse>("trailers");
        trailresAtom.change("trailers", data.data);
      } catch (error) {
        if (error instanceof AxiosError) {
          toast.error(error.response?.data.message);
        } else {
          toast.error((error as Error).message);
        }
      }
    },

    async getOneTrailer(slug: string) {
      try {
        const { data } = await endpoint.get<GetOneTrailerResponse>(
          `trailers/${slug}`,
        );
        trailresAtom.change("oneTrailer", data.data);
      } catch (error) {
        if (error instanceof AxiosError) {
          toast.error(error.response?.data.message);
        } else {
          toast.error((error as Error).message);
        }
      }
    },

    async createTrailer(
      formData: CreateTrailerFormData,
      onSuccess?: () => void,
    ) {
      try {
        const { data } = await endpoint.post<CreateTrailerResponse>(
          "trailers",
          formData,
        );
        toast.success(data.message);
        onSuccess?.();
        trailresAtom.getTrailers();
      } catch (error) {
        if (error instanceof AxiosError) {
          toast.error(error.response?.data.message);
        } else {
          toast.error((error as Error).message);
        }
      }
    },

    async editTrailer(
      slug: string,
      formData: CreateTrailerFormData,
      onSuccess?: () => void,
    ) {
      try {
        const { data } = await endpoint.put<UpdateTrailerResponse>(
          `trailers/${slug}`,
          formData,
        );
        toast.success(data.message);
        onSuccess?.();
        trailresAtom.getTrailers();
      } catch (error) {
        if (error instanceof AxiosError) {
          toast.error(error.response?.data.message);
        } else {
          toast.error((error as Error).message);
        }
      }
    },

    async deleteTrailer(slug: string, onSuccess?: () => void) {
      try {
        const { data } = await endpoint.delete<DeleteTrailerResponse>(
          `trailers/${slug}`,
        );
        toast.success(data.data);
        onSuccess?.();
        trailresAtom.getTrailers();
      } catch (error) {
        if (error instanceof AxiosError) {
          toast.error(error.response?.data.message);
        } else {
          toast.error((error as Error).message);
        }
      }
    },
  },
});
