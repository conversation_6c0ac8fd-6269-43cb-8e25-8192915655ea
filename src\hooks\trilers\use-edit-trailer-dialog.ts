import { openUpdateTrailer<PERSON>tom } from "@/atoms/app/open-atoms";
import { selectedTrailerAtom } from "@/atoms/app/selected-atoms";
import { trailresAtom } from "@/atoms/entities/trailers-atom";
import type { CreateTrailerFormData } from "@/types/trailers";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { z } from "zod";

export default function useEditTrailerDialog() {
  const { t } = useTranslation();

  const FormSchema = z.object({
    name: z.string().nonempty(t("trailers.validation.name_required")),
    description: z
      .string()
      .nonempty(t("trailers.validation.description_required")),
    is_active: z.boolean(),
  });

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      name: "",
      description: "",
      is_active: false,
    },
  });

  const { slug } = selectedTrailerAtom.useValue();
  const { oneTrailer } = trailresAtom.useValue();

  const isOpened = openUpdateTrailerAtom.useOpened();
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingFetchTrailer, setIsLoadingFetchTrailer] = useState(false);

  async function onSubmit(data: z.infer<typeof FormSchema>) {
    const formData: CreateTrailerFormData = {
      name: data.name,
      description: data.description,
      is_active: data.is_active ? 1 : 0,
    };

    setIsLoading(true);

    if (slug) {
      await trailresAtom.editTrailer(slug, formData, () => {
        closeDialog();
      });
    }

    setIsLoading(false);
  }

  const closeDialog = () => {
    openUpdateTrailerAtom.close();
    selectedTrailerAtom.reset();
    trailresAtom.change("oneTrailer", null);
  };

  useEffect(() => {
    async function fetchTrailer() {
      if (slug) {
        setIsLoadingFetchTrailer(true);
        await trailresAtom.getOneTrailer(slug);
        setIsLoadingFetchTrailer(false);
      }
    }

    fetchTrailer();
  }, [slug]);

  useEffect(() => {
    form.reset({
      name: oneTrailer?.name,
      description: oneTrailer?.description,
      is_active: oneTrailer?.is_active,
    });
  }, [form, oneTrailer]);

  return {
    form,
    onSubmit,
    closeDialog,
    isOpened,
    isLoading,
    isLoadingFetchTrailer,
    t,
  };
}
