import { cn } from "@/lib/utils";
import * as React from "react";

interface ColorInputProps {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

const ColorInput = React.forwardRef<HTMLInputElement, ColorInputProps>(
  (
    { className, value = "#000000", onChange, placeholder, disabled, ...props },
    ref,
  ) => {
    const [internalValue, setInternalValue] = React.useState(value);
    const hiddenInputRef = React.useRef<HTMLInputElement>(null);

    React.useEffect(() => {
      setInternalValue(value);
    }, [value]);

    const handleColorChange = (event: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = event.target.value;
      setInternalValue(newValue);
      onChange?.(newValue);
    };

    const handleTriggerClick = () => {
      if (!disabled && hiddenInputRef.current) {
        hiddenInputRef.current.click();
      }
    };

    return (
      <div className="relative">
        <button
          type="button"
          onClick={handleTriggerClick}
          disabled={disabled}
          className={cn(
            // Base styles
            "bg-background/50 flex h-10 w-full items-center justify-between gap-3 rounded-lg border px-4 py-2 text-sm backdrop-blur-sm transition-all duration-200 outline-none",
            // Border and background
            "border-border/50 dark:bg-input/20 dark:border-input/30",
            // Placeholder and text
            "placeholder:text-muted-foreground/70 text-foreground",
            // Focus states with gradient ring
            "focus:bg-background/80 focus:border-transparent",
            "focus:ring-2 focus:ring-fuchsia-500/30",
            "focus:shadow-lg focus:shadow-fuchsia-500/10",
            // Hover states
            "hover:border-border hover:bg-background/70 hover:shadow-md",
            // Invalid states
            "aria-invalid:border-destructive/50 aria-invalid:ring-destructive/20",
            "dark:aria-invalid:ring-destructive/30 aria-invalid:bg-destructive/5",
            // Disabled states
            "disabled:bg-muted/30 disabled:cursor-not-allowed disabled:opacity-50",
            className,
          )}
          {...props}
        >
          <div className="flex items-center gap-3">
            <div
              className="border-border/30 h-5 w-5 rounded-md border-2 shadow-sm transition-all duration-200 hover:scale-110"
              style={{ backgroundColor: internalValue }}
            />
            <span className="text-sm font-medium">
              {internalValue || placeholder || "Select color"}
            </span>
          </div>
        </button>
        <input
          ref={(node) => {
            hiddenInputRef.current = node;
            if (typeof ref === "function") {
              ref(node);
            } else if (ref) {
              ref.current = node;
            }
          }}
          type="color"
          value={internalValue}
          onChange={handleColorChange}
          className="absolute inset-0 h-full w-full cursor-pointer opacity-0"
          disabled={disabled}
        />
      </div>
    );
  },
);

ColorInput.displayName = "ColorInput";

export { ColorInput };
