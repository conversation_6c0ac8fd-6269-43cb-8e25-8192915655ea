export const areasTranslations = {
  title: "Areas",
  add: "Add Area",

  form: {
    name_placeholder: "Enter area name",
    code_placeholder: "Enter area code",
    type_placeholder: "Select type",
    circle: "Circle",
    polygon: "Polygon",
    area_group: "Area Group",
    area_group_placeholder: "Select area group",
    color_placeholder: "Select color",
    is_personal: "Is Personal",
    no_area_group_found: "No area groups found",
    no_type_found: "No types found",
  },

  add_area: {
    creating: "Creating...",
  },

  edit_area: {
    editing: "Editing...",
  },

  table: {
    selectAll: "Select all",
    selectRow: "Select row",
    type: "Type",
    code: "Code",
    circle: "Circle",
    polygon: "Polygon",
    group: "Area Group",
  },

  actions: {
    title: "Actions",
    message: "Message",
    track: "Track",
    dataAccuracy: "Data Accuracy",
  },

  validation: {
    name_required: "Name is required",
    code_required: "Code is required",
    type_required: "Type is required",
    color_required: "Color is required",
    area_group_required: "Area Group is required",
  },
};
