import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogScrollArea,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import useCreateUnitDialog from "@/hooks/units/use-create-unit-dialog";
import { cn } from "@/lib/utils";
import i18n from "@/localization/i18n";
import { Check, ChevronsUpDown } from "lucide-react";

export default function CreateUnitDialog() {
  const {
    closeDialog,
    form,
    isLoading,
    isOpened,
    onSubmit,
    t,
    constants,
    models,
  } = useCreateUnitDialog();

  return (
    <Dialog open={isOpened} onOpenChange={closeDialog}>
      <DialogContent dir={i18n.language === "ar" ? "rtl" : "ltr"} size="xl">
        <DialogHeader>
          <DialogTitle>{t("units.create.title")}</DialogTitle>
          <DialogDescription>{t("units.create.description")}</DialogDescription>
        </DialogHeader>

        <DialogScrollArea>
          <div className="space-y-6">
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-6"
              >
                {/* Basic Info */}
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("units.fields.name")}</FormLabel>
                        <FormControl>
                          <Input
                            placeholder={t("units.placeholders.name")}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="plate_number"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("units.fields.plate_number")}</FormLabel>
                        <FormControl>
                          <Input
                            placeholder={t("units.placeholders.plate_number")}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Device Info */}
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="device_type"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>{t("units.fields.device_type")}</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant="outline"
                                role="combobox"
                                className={cn(
                                  "dark:bg-input/20 dark:border-input/30 w-full justify-between",
                                  !field.value && "text-muted-foreground",
                                )}
                              >
                                {field.value
                                  ? constants?.devices.find(
                                      (t) => t.id === Number(field.value),
                                    )?.name
                                  : t("units.placeholders.device_type")}
                                <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0">
                            <Command>
                              <CommandInput
                                placeholder={t(
                                  "units.placeholders.device_type",
                                )}
                                className="h-9"
                              />
                              <CommandList>
                                <CommandEmpty>
                                  {t("units.create.no_device_type_found")}
                                </CommandEmpty>
                                <CommandGroup>
                                  {constants?.devices.map(
                                    ({ id, name, title }) => (
                                      <CommandItem
                                        key={id}
                                        // 👇 must be string
                                        value={`${name} ${title}`}
                                        onSelect={() =>
                                          field.onChange(String(id))
                                        }
                                      >
                                        <Check
                                          className={cn(
                                            "mr-2 h-4 w-4",
                                            Number(field.value) === id
                                              ? "opacity-100"
                                              : "opacity-0",
                                          )}
                                        />
                                        {`${title} - ${name}`}
                                      </CommandItem>
                                    ),
                                  )}
                                </CommandGroup>
                              </CommandList>
                            </Command>
                          </PopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="device_serial_number"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t("units.fields.device_serial_number")}
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder={t(
                              "units.placeholders.device_serial_number",
                            )}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* SIM & IMEI */}
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="sim_card_number"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t("units.fields.sim_card_number")}
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder={t(
                              "units.placeholders.sim_card_number",
                            )}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="sim_card_serial_number"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t("units.fields.sim_card_serial_number")}
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder={t(
                              "units.placeholders.sim_card_serial_number",
                            )}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="imei"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("units.fields.imei")}</FormLabel>
                      <FormControl>
                        <Input
                          placeholder={t("units.placeholders.imei")}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Password & Operation Code */}
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="password"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("units.fields.password")}</FormLabel>
                        <FormControl>
                          <Input
                            type="password"
                            placeholder={t("units.placeholders.password")}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="operation_code"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t("units.fields.operation_code")}
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder={t("units.placeholders.operation_code")}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Engine Hours */}
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="engine_hours_type"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>
                          {t("units.fields.engine_hours_type")}
                        </FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant="outline"
                                role="combobox"
                                className={cn(
                                  "dark:bg-input/20 dark:border-input/30 w-full justify-between",
                                  !field.value && "text-muted-foreground",
                                )}
                              >
                                {field.value
                                  ? constants?.engine_hours.find(
                                      (t) => t.id === Number(field.value),
                                    )?.name
                                  : t("units.placeholders.engine_hours_type")}
                                <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0">
                            <Command>
                              <CommandInput
                                placeholder={t(
                                  "units.placeholders.engine_hours_type",
                                )}
                                className="h-9"
                              />
                              <CommandList>
                                <CommandEmpty>
                                  {t("units.create.no_engine_hours_type_found")}
                                </CommandEmpty>
                                <CommandGroup>
                                  {constants?.engine_hours.map(
                                    ({ id, name, title }) => (
                                      <CommandItem
                                        key={id}
                                        // 👇 must be string
                                        value={`${name} ${title}`}
                                        onSelect={() =>
                                          field.onChange(String(id))
                                        }
                                      >
                                        <Check
                                          className={cn(
                                            "mr-2 h-4 w-4",
                                            +field.value === id
                                              ? "opacity-100"
                                              : "opacity-0",
                                          )}
                                        />
                                        {`${title} - ${name}`}
                                      </CommandItem>
                                    ),
                                  )}
                                </CommandGroup>
                              </CommandList>
                            </Command>
                          </PopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="engine_hours_value"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t("units.fields.engine_hours_value")}
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder={t(
                              "units.placeholders.engine_hours_value",
                            )}
                            {...field}
                            min={0}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Odometer */}
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="odometer_type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("units.fields.odometer_type")}</FormLabel>
                        <FormControl>
                          <Input
                            placeholder={t("units.placeholders.odometer_type")}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="odometer_val"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("units.fields.odometer_val")}</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder={t("units.placeholders.odometer_val")}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Protocol, Business, Vehicle */}
                <div className="grid grid-cols-2 gap-4">
                  {/* <FormField
                control={form.control}
                name="protocol_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("units.fields.protocol_id")}</FormLabel>
                    <FormControl>
                      <Input
                        placeholder={t("units.placeholders.protocol_id")}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              /> */}

                  <FormField
                    control={form.control}
                    name="protocol_id"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>{t("units.fields.protocol_id")}</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant="outline"
                                role="combobox"
                                className={cn(
                                  "dark:bg-input/20 dark:border-input/30 w-full justify-between",
                                  !field.value && "text-muted-foreground",
                                )}
                              >
                                {field.value
                                  ? models?.Protocol.find(
                                      (type) => type.id === +field.value,
                                    )?.name
                                  : t("units.placeholders.protocol_id")}
                                <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-full p-0">
                            <Command>
                              <CommandInput
                                placeholder={t(
                                  "units.placeholders.protocol_id",
                                )}
                                className="h-9"
                              />
                              <CommandList>
                                <CommandEmpty>
                                  {t("units.create.no_business_type_found")}
                                </CommandEmpty>
                                <CommandGroup>
                                  {models?.Protocol.map(({ id, name }) => (
                                    <CommandItem
                                      key={id}
                                      // 👇 must be string
                                      value={`${name}`}
                                      onSelect={() =>
                                        field.onChange(String(id))
                                      }
                                    >
                                      {name}
                                      <Check
                                        className={cn(
                                          "ml-auto h-4 w-4",
                                          +field.value === id
                                            ? "opacity-100"
                                            : "opacity-0",
                                        )}
                                      />
                                    </CommandItem>
                                  ))}
                                </CommandGroup>
                              </CommandList>
                            </Command>
                          </PopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="business_type"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>{t("units.fields.business_type")}</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant="outline"
                                role="combobox"
                                className={cn(
                                  "dark:bg-input/20 dark:border-input/30 w-full justify-between",
                                  !field.value && "text-muted-foreground",
                                )}
                              >
                                {field.value
                                  ? constants?.business.find(
                                      (type) => type.id === +field.value,
                                    )?.name
                                  : t("units.placeholders.business_type")}
                                <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-full p-0">
                            <Command>
                              <CommandInput
                                placeholder={t(
                                  "units.placeholders.business_type",
                                )}
                                className="h-9"
                              />
                              <CommandList>
                                <CommandEmpty>
                                  {t("units.create.no_business_type_found")}
                                </CommandEmpty>
                                <CommandGroup>
                                  {constants?.business.map(
                                    ({ id, name, title }) => (
                                      <CommandItem
                                        key={id}
                                        // 👇 must be string
                                        value={`${name} ${title}`}
                                        onSelect={() =>
                                          field.onChange(String(id))
                                        }
                                      >
                                        {`${title} - ${name}`}
                                        <Check
                                          className={cn(
                                            "ml-auto h-4 w-4",
                                            +field.value === id
                                              ? "opacity-100"
                                              : "opacity-0",
                                          )}
                                        />
                                      </CommandItem>
                                    ),
                                  )}
                                </CommandGroup>
                              </CommandList>
                            </Command>
                          </PopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="vehicle_type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("units.fields.vehicle_type")}</FormLabel>
                        <FormControl>
                          <Input
                            placeholder={t("units.placeholders.vehicle_type")}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="measurement_type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t("units.fields.measurement_type")}
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder={t(
                              "units.placeholders.measurement_type",
                            )}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Measurement */}
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="max_capacity"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("units.fields.max_capacity")}</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder={t("units.placeholders.max_capacity")}
                            {...field}
                            min={0}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="seats"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("units.fields.seats")}</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder={t("units.placeholders.seats")}
                            {...field}
                            min={0}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </form>
            </Form>
          </div>
        </DialogScrollArea>

        <DialogFooter>
          <DialogClose asChild>
            <Button variant="gradient-outline" type="button" size="lg">
              {t("units.create.buttons.cancel")}
            </Button>
          </DialogClose>
          <Button
            variant="gradient"
            type="submit"
            disabled={isLoading}
            size="lg"
            onClick={form.handleSubmit(onSubmit)}
          >
            {isLoading
              ? t("units.create.buttons.submitting")
              : t("units.create.buttons.submit")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
