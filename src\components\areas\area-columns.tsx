import { openConfirmDeleteAreaDialog<PERSON>tom } from "@/atoms/app/open-atoms";
import { selectedAreaToDeleteAtom } from "@/atoms/app/selected-atoms";
import type { Area } from "@/types/areas";
import { URLS } from "@/utils/urls";
import type { TFunction } from "i18next";
import { Edit, Locate, MessageCircle, Trash2, Wifi } from "lucide-react";
import { useNavigate } from "react-router";
import { createColumns } from "../utils/columns-factory";

export const AreaColumns = (t: TFunction) => {
  const navigate = useNavigate();

  return createColumns<Area>({
    t,
    entityKey: "areas",
    fields: [
      {
        key: "name",
        title: "common.table.name",
      },
      {
        key: "type",
        title: "areas.table.type",
        cell: (area) =>
          area.type === 1 ? t("areas.table.circle") : t("areas.table.polygon"),
      },
      {
        key: "area_group",
        title: "areas.table.group",
      },
      {
        key: "code",
        title: "areas.table.code",
      },
    ],
    actions: [
      {
        icon: <MessageCircle />,
        label: t("areas.actions.message"),
        onClick: (row) => {
          // custom handler (message logic goes here)
          console.log("Send message to area:", row.slug);
        },
      },
      {
        icon: <Locate />,
        label: t("areas.actions.track"),
        onClick: (row) => {
          // tracking logic
          console.log("Track area:", row.slug);
        },
      },
      {
        icon: <Wifi />,
        label: t("areas.actions.dataAccuracy"),
        onClick: (row) => {
          // accuracy logic
          console.log("Check accuracy for:", row.slug);
        },
      },
      {
        icon: <Edit />,
        label: t("common.actions.edit"),
        onClick: (row) => navigate(`${URLS.editArea}/${row.slug}`), // ✅ يروح للصفحة
      },
      {
        icon: <Trash2 color="red" />,
        label: t("common.actions.delete"),
        onClick: (row) => {
          openConfirmDeleteAreaDialogAtom.open();
          selectedAreaToDeleteAtom.change("slug", row.slug);
        },
      },
    ],
  });
};
