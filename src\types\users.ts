export interface GetAllUsersResponse {
  status_code: number;
  status: string;
  message: null;
  data: User[];
}

export interface GetOneUserResponse {
  status_code: number;
  status: string;
  message: null;
  data: User;
}

export interface UpdateUserResponse {
  status_code: number;
  status: string;
  message: string;
  data: User;
}

export interface CreateUserResponse {
  status_code: number;
  status: string;
  message: string;
  data: User;
}

export interface DeleteUserResponse {
  status_code: number;
  status: string;
  message: null;
  data: string;
}

export interface CreateUserFormData {
  name: string;
  email: string;
  password: string;
  password_confirmation: string;
  phone_code: string;
  phone: string;
  lang: "en" | "ar";
}

export interface UpdateUserFormData {
  name: string;
  email: string;
  // password: string;
  // password_confirmation: string;
  phone_code: string;
  phone: string;
  lang: "en" | "ar";
}

export interface User {
  id: number;
  slug: string;
  name: string;
  email: string;
  phone_code: string;
  phone: string;
  image: string;
  lang: string;
  is_active: number;
}
