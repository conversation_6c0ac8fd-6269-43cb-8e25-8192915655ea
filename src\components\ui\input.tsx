import * as React from "react";

import { cn } from "@/lib/utils";

function Input({ className, type, ...props }: React.ComponentProps<"input">) {
  return (
    <input
      type={type}
      data-slot="input"
      className={cn(
        // Base styles
        "bg-background/50 flex h-10 w-full min-w-0 rounded-lg border px-4 py-2 text-base transition-all duration-200 outline-none",
        // Border and background
        "border-border/50 backdrop-blur-sm",
        "dark:bg-input/20 dark:border-input/30",
        // Placeholder and text
        "placeholder:text-muted-foreground/70 text-foreground",
        "selection:bg-primary/20 selection:text-primary-foreground",
        // Focus states with gradient ring
        "focus:bg-background/80 focus:border-transparent",
        "focus:ring-gradient-to-r focus:ring-2 focus:ring-cyan-400/30",
        "focus:shadow-lg focus:shadow-fuchsia-500/10",
        // Hover states
        "hover:border-border hover:bg-background/70 hover:shadow-md",
        // Invalid states
        "aria-invalid:border-destructive/50 aria-invalid:ring-destructive/20",
        "dark:aria-invalid:ring-destructive/30 aria-invalid:bg-destructive/5",
        // File input styles
        "file:text-foreground file:inline-flex file:h-8 file:border-0 file:bg-gradient-to-r file:from-fuchsia-500/10 file:to-cyan-400/10",
        "file:mr-3 file:rounded-md file:px-3 file:py-1 file:text-sm file:font-medium",
        "file:cursor-pointer file:transition-all file:hover:from-fuchsia-500/20 file:hover:to-cyan-400/20",
        // Disabled states
        "disabled:bg-muted/30 disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50",
        // Responsive text size
        "md:text-sm",
        className,
      )}
      {...props}
    />
  );
}

export { Input };
