import * as React from "react";

import { cn } from "@/lib/utils";

function Input({ className, type, ...props }: React.ComponentProps<"input">) {
  return (
    <input
      type={type}
      data-slot="input"
      className={cn(
        // Base styles
        "flex h-11 w-full min-w-0 rounded-xl border px-4 py-3 text-base transition-all duration-300 outline-none",
        // Glass morphism background
        "bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl border-white/10",
        // Placeholder and text
        "placeholder:text-slate-400 text-slate-200",
        "selection:bg-cyan-400/20 selection:text-cyan-100",
        // Focus states
        "focus:bg-gradient-to-br focus:from-white/15 focus:to-white/8 focus:border-cyan-400/50",
        "focus:ring-2 focus:ring-cyan-400/20 focus:shadow-lg focus:shadow-cyan-400/10",
        // Hover states
        "hover:border-white/20 hover:bg-gradient-to-br hover:from-white/12 hover:to-white/6 hover:shadow-md",
        // Invalid states
        "aria-invalid:border-red-400/50 aria-invalid:ring-red-400/20 aria-invalid:bg-red-500/5",
        // File input styles
        "file:text-slate-200 file:inline-flex file:h-8 file:border-0 file:bg-gradient-to-r file:from-cyan-400/20 file:to-blue-500/20",
        "file:mr-3 file:rounded-lg file:px-3 file:py-1 file:text-sm file:font-medium file:border file:border-cyan-400/30",
        "file:cursor-pointer file:transition-all file:hover:from-cyan-400/30 file:hover:to-blue-500/30 file:hover:scale-105",
        // Disabled states
        "disabled:bg-slate-800/30 disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 disabled:text-slate-500",
        // Responsive text size
        "md:text-sm",
        className,
      )}
      {...props}
    />
  );
}

export { Input };
