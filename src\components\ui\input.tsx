import * as React from "react";

import { cn } from "@/lib/utils";

function Input({
  className,
  type,
  style,
  onFocus,
  onBlur,
  ...props
}: React.ComponentProps<"input">) {
  const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
    e.target.style.borderColor =
      "oklch(from var(--brand-secondary) l c h / 0.5)";
    e.target.style.setProperty(
      "--tw-ring-color",
      "oklch(from var(--brand-secondary) l c h / 0.2)",
    );
    e.target.style.setProperty(
      "--tw-ring-shadow",
      "var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color)",
    );
    e.target.style.boxShadow =
      "var(--tw-ring-offset-shadow), var(--tw-ring-shadow), 0 10px 15px -3px oklch(from var(--brand-secondary) l c h / 0.1)";
    onFocus?.(e);
  };

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    e.target.style.borderColor = "";
    e.target.style.setProperty("--tw-ring-color", "");
    e.target.style.setProperty("--tw-ring-shadow", "");
    e.target.style.boxShadow = "";
    onBlur?.(e);
  };

  return (
    <input
      type={type}
      data-slot="input"
      className={cn(
        // Base styles
        "flex h-11 w-full min-w-0 rounded-xl border px-4 py-3 text-base transition-all duration-300 outline-none",
        // Glass morphism background
        "border-white/10 bg-gradient-to-br from-slate-900/95 via-slate-800/90 to-slate-900/95 backdrop-blur-2xl",
        // Placeholder and text
        "text-slate-200 placeholder:text-slate-400",
        // Focus states
        "focus:bg-gradient-to-br focus:from-slate-800/95 focus:via-slate-700/90 focus:to-slate-800/95 focus:shadow-lg focus:ring-2",
        // Hover states
        "hover:border-white/20 hover:bg-gradient-to-br hover:from-slate-800/95 hover:via-slate-700/90 hover:to-slate-800/95 hover:shadow-md",
        // Invalid states
        "aria-invalid:border-red-400/50 aria-invalid:bg-red-500/5 aria-invalid:ring-red-400/20",
        // Disabled states
        "disabled:pointer-events-none disabled:cursor-not-allowed disabled:bg-slate-800/30 disabled:text-slate-500 disabled:opacity-50",
        // Responsive text size
        "md:text-sm",
        className,
      )}
      style={{
        ...style,
      }}
      onFocus={handleFocus}
      onBlur={handleBlur}
      {...props}
    />
  );
}

export { Input };
