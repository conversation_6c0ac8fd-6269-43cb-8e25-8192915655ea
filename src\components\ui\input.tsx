import * as React from "react";

import { cn } from "@/lib/utils";

function Input({ className, type, ...props }: React.ComponentProps<"input">) {
  return (
    <input
      type={type}
      data-slot="input"
      className={cn(
        // Base styles
        "flex h-11 w-full min-w-0 rounded-xl border px-4 py-3 text-base transition-all duration-300 outline-none",
        // Glass morphism background
        "border-white/10 bg-gradient-to-br from-slate-900/95 via-slate-800/90 to-slate-900/95 backdrop-blur-2xl",
        // Placeholder and text
        "text-slate-200 placeholder:text-slate-400",
        "selection:bg-cyan-400/20 selection:text-cyan-100",
        // Focus states
        "focus:bg-gradient-to-br focus:from-slate-800/95 focus:via-slate-700/90 focus:to-slate-800/95 focus:shadow-lg focus:ring-2",
        // Hover states
        "hover:border-white/20 hover:bg-gradient-to-br hover:from-slate-800/95 hover:via-slate-700/90 hover:to-slate-800/95 hover:shadow-md",
        // Invalid states
        "aria-invalid:border-red-400/50 aria-invalid:bg-red-500/5 aria-invalid:ring-red-400/20",
        // File input styles
        "file:inline-flex file:h-8 file:border-0 file:bg-gradient-to-r file:from-cyan-400/20 file:to-blue-500/20 file:text-slate-200",
        "file:mr-3 file:rounded-lg file:border file:border-cyan-400/30 file:px-3 file:py-1 file:text-sm file:font-medium",
        "file:cursor-pointer file:transition-all file:hover:scale-105 file:hover:from-cyan-400/30 file:hover:to-blue-500/30",
        // Disabled states
        "disabled:pointer-events-none disabled:cursor-not-allowed disabled:bg-slate-800/30 disabled:text-slate-500 disabled:opacity-50",
        // Responsive text size
        "md:text-sm",
        className,
      )}
      {...props}
    />
  );
}

export { Input };
