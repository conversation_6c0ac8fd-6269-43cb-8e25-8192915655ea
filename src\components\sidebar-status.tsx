import { Badge } from "@/components/ui/badge";
import { useSidebar } from "@/components/ui/sidebar";
import { Activity, Wifi, WifiOff } from "lucide-react";
import { useTranslation } from "react-i18next";

export function SidebarStatus() {
  const { state } = useSidebar();
  const { t } = useTranslation();

  // Mock status data - replace with real data
  const isOnline = true;
  const activeUsers = 12;
  const systemStatus = t("ui.sidebar.operational");

  if (state === "collapsed") {
    return (
      <div className="flex flex-col items-center gap-2 p-2">
        <div
          className="flex h-8 w-8 items-center justify-center rounded-lg"
          style={{
            background: `linear-gradient(to right,
              oklch(from var(--brand-secondary) l c h / 0.1),
              oklch(from var(--brand-primary) l c h / 0.1))`,
          }}
        >
          {isOnline ? (
            <Wifi
              className="h-4 w-4"
              style={{ color: "var(--brand-secondary)" }}
            />
          ) : (
            <WifiOff className="h-4 w-4" style={{ color: "var(--error)" }} />
          )}
        </div>
        <div
          className="flex h-8 w-8 items-center justify-center rounded-lg"
          style={{
            background: `linear-gradient(to right,
              oklch(from var(--brand-primary) l c h / 0.1),
              oklch(from var(--brand-secondary) l c h / 0.1))`,
          }}
        >
          <Activity
            className="h-4 w-4"
            style={{ color: "var(--brand-primary)" }}
          />
        </div>
      </div>
    );
  }

  return (
    <div
      className="space-y-3 rounded-xl p-3"
      style={{
        background: `linear-gradient(to right,
          oklch(from var(--brand-secondary) l c h / 0.1),
          oklch(from var(--brand-primary) l c h / 0.1))`,
      }}
    >
      <div className="flex items-center justify-between">
        <span className="text-muted-foreground text-xs font-medium">
          {t("ui.sidebar.systemStatus")}
        </span>
        <Badge
          variant="secondary"
          className="text-xs font-medium"
          style={{
            background: `linear-gradient(to right,
              oklch(from var(--brand-secondary) l c h / 0.2),
              oklch(from var(--brand-primary) l c h / 0.2))`,
            color: "var(--brand-secondary)",
          }}
        >
          {systemStatus}
        </Badge>
      </div>

      <div className="flex items-center gap-2">
        {isOnline ? (
          <Wifi
            className="h-4 w-4"
            style={{ color: "var(--brand-secondary)" }}
          />
        ) : (
          <WifiOff className="h-4 w-4" style={{ color: "var(--error)" }} />
        )}
        <span className="text-muted-foreground text-xs">
          {isOnline ? t("ui.sidebar.connected") : t("ui.sidebar.offline")}
        </span>
      </div>

      <div className="flex items-center gap-2">
        <Activity
          className="h-4 w-4"
          style={{ color: "var(--brand-primary)" }}
        />
        <span className="text-muted-foreground text-xs">
          {activeUsers} {t("ui.sidebar.activeUsers")}
        </span>
      </div>
    </div>
  );
}
