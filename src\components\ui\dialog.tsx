import * as DialogPrimitive from "@radix-ui/react-dialog";
import { XIcon } from "lucide-react";
import * as React from "react";

import { cn } from "@/lib/utils";

function Dialog({
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Root>) {
  return <DialogPrimitive.Root data-slot="dialog" {...props} />;
}

function DialogTrigger({
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {
  return <DialogPrimitive.Trigger data-slot="dialog-trigger" {...props} />;
}

function DialogPortal({
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Portal>) {
  return <DialogPrimitive.Portal data-slot="dialog-portal" {...props} />;
}

function DialogClose({
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Close>) {
  return <DialogPrimitive.Close data-slot="dialog-close" {...props} />;
}

function DialogOverlay({
  className,
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {
  return (
    <DialogPrimitive.Overlay
      data-slot="dialog-overlay"
      className={cn(
        "data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-gradient-to-br from-black/60 via-fuchsia-900/20 to-cyan-900/20 backdrop-blur-sm",
        className,
      )}
      {...props}
    />
  );
}

function DialogContent({
  className,
  children,
  showCloseButton = true,
  size = "default",
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Content> & {
  showCloseButton?: boolean;
  size?: "sm" | "default" | "lg" | "xl" | "full";
}) {
  const sizeClasses = {
    sm: "sm:max-w-sm max-h-[85vh]",
    default: "sm:max-w-lg max-h-[85vh]",
    lg: "sm:max-w-2xl max-h-[90vh]",
    xl: "sm:max-w-4xl max-h-[90vh]",
    full: "sm:max-w-[95vw] max-h-[95vh]",
  };

  return (
    <DialogPortal data-slot="dialog-portal">
      <DialogOverlay />
      <DialogPrimitive.Content
        data-slot="dialog-content"
        className={cn(
          "from-background/95 to-background/90 relative bg-gradient-to-br backdrop-blur-xl",
          "data-[state=open]:animate-in data-[state=closed]:animate-out",
          "data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
          "data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95",
          "data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%]",
          "data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%]",
          "fixed top-[50%] left-[50%] z-50 flex w-full max-w-[calc(100%-2rem)] flex-col",
          "translate-x-[-50%] translate-y-[-50%] rounded-3xl border-0",
          "overflow-hidden shadow-2xl duration-300",
          "before:absolute before:inset-0 before:rounded-3xl before:bg-gradient-to-br before:from-fuchsia-500/10 before:to-cyan-400/10 before:opacity-50",
          sizeClasses[size],
          className,
        )}
        {...props}
      >
        <div className="relative z-10 flex h-full flex-col">{children}</div>
        {showCloseButton && (
          <DialogPrimitive.Close
            data-slot="dialog-close"
            className={cn(
              "absolute end-6 top-6 z-20 cursor-pointer rounded-full p-2",
              "from-muted/80 to-muted/60 bg-gradient-to-br backdrop-blur-sm",
              "text-muted-foreground hover:text-foreground",
              "transition-all duration-200 hover:scale-110 hover:rotate-90",
              "hover:bg-gradient-to-br hover:from-fuchsia-500/20 hover:to-cyan-400/20",
              "focus:ring-2 focus:ring-fuchsia-500/50 focus:ring-offset-2 focus:outline-hidden",
              "disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0",
              "[&_svg:not([class*='size-'])]:size-4",
            )}
          >
            <XIcon />
            <span className="sr-only">Close</span>
          </DialogPrimitive.Close>
        )}
      </DialogPrimitive.Content>
    </DialogPortal>
  );
}

function DialogHeader({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="dialog-header"
      className={cn(
        "flex flex-shrink-0 flex-col gap-3 text-center sm:text-start",
        "border-gradient-to-r border-b from-fuchsia-500/20 to-cyan-400/20 p-6 pb-4",
        className,
      )}
      {...props}
    />
  );
}

function DialogFooter({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="dialog-footer"
      className={cn(
        "flex flex-shrink-0 flex-col-reverse gap-3 sm:flex-row sm:justify-end",
        "border-gradient-to-r border-t from-fuchsia-500/20 to-cyan-400/20 p-6 pt-4",
        className,
      )}
      {...props}
    />
  );
}

function DialogScrollArea({
  className,
  children,
  ...props
}: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="dialog-scroll-area"
      className={cn(
        "dialog-scroll max-h-[400px] flex-1 overflow-y-auto px-6 py-4",
        // Smooth scrolling and enhanced UX
        "scroll-smooth",
        "transition-all duration-200",
        // Focus and hover states
        "focus-within:shadow-inner",
        "hover:bg-gradient-to-b hover:from-transparent hover:to-fuchsia-500/5",
        className,
      )}
      {...props}
    >
      {children}
    </div>
  );
}

function DialogTitle({
  className,
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Title>) {
  return (
    <DialogPrimitive.Title
      data-slot="dialog-title"
      className={cn(
        "text-xl leading-tight font-bold",
        "bg-gradient-to-r from-fuchsia-500 to-cyan-400 bg-clip-text text-transparent",
        className,
      )}
      {...props}
    />
  );
}

function DialogDescription({
  className,
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Description>) {
  return (
    <DialogPrimitive.Description
      data-slot="dialog-description"
      className={cn(
        "text-muted-foreground text-base leading-relaxed",
        className,
      )}
      {...props}
    />
  );
}

export {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogOverlay,
  DialogPortal,
  DialogScrollArea,
  DialogTitle,
  DialogTrigger,
};
