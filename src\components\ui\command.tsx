"use client";

import { Command as CommandPrimitive } from "cmdk";
import { SearchIcon } from "lucide-react";
import * as React from "react";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { cn } from "@/lib/utils";

function Command({
  className,
  ...props
}: React.ComponentProps<typeof CommandPrimitive>) {
  return (
    <CommandPrimitive
      data-slot="command"
      className={cn(
        // Base styles with modern design
        "border-border/50 flex h-full w-full flex-col overflow-hidden rounded-lg border shadow-xl backdrop-blur-sm",
        // Background with gradient
        "bg-popover/95 text-popover-foreground",
        "from-background/95 to-background/90 bg-gradient-to-b",
        className,
      )}
      {...props}
    />
  );
}

function CommandDialog({
  title = "Command Palette",
  description = "Search for a command to run...",
  children,
  className,
  showCloseButton = true,
  ...props
}: React.ComponentProps<typeof Dialog> & {
  title?: string;
  description?: string;
  className?: string;
  showCloseButton?: boolean;
}) {
  return (
    <Dialog {...props}>
      <DialogHeader className="sr-only">
        <DialogTitle>{title}</DialogTitle>
        <DialogDescription>{description}</DialogDescription>
      </DialogHeader>
      <DialogContent
        className={cn(
          "border-border/50 overflow-hidden p-0 shadow-2xl",
          className,
        )}
        showCloseButton={showCloseButton}
      >
        <Command className="[&_[cmdk-group-heading]]:text-muted-foreground/80 [&_[cmdk-group-heading]]:px-3 [&_[cmdk-group-heading]]:font-semibold [&_[cmdk-group]]:px-2 [&_[cmdk-group]:not([hidden])_~[cmdk-group]]:pt-0 [&_[cmdk-input-wrapper]_svg]:h-5 [&_[cmdk-input-wrapper]_svg]:w-5 [&_[cmdk-input]]:h-12 [&_[cmdk-item]]:px-3 [&_[cmdk-item]]:py-3 [&_[cmdk-item]_svg]:h-5 [&_[cmdk-item]_svg]:w-5">
          {children}
        </Command>
      </DialogContent>
    </Dialog>
  );
}

function CommandInput({
  className,
  ...props
}: React.ComponentProps<typeof CommandPrimitive.Input>) {
  return (
    <div
      data-slot="command-input-wrapper"
      className="border-border/30 flex h-12 items-center gap-3 border-b bg-gradient-to-r from-fuchsia-500/5 to-cyan-400/5 px-4"
    >
      <SearchIcon className="text-muted-foreground/70 size-5 shrink-0 transition-colors" />
      <CommandPrimitive.Input
        data-slot="command-input"
        className={cn(
          // Base styles
          "flex h-full w-full bg-transparent py-3 text-sm outline-hidden transition-all duration-200",
          // Placeholder and text
          "placeholder:text-muted-foreground/70 text-foreground",
          // Focus states
          "focus:placeholder:text-muted-foreground/50",
          // Disabled states
          "disabled:cursor-not-allowed disabled:opacity-50",
          className,
        )}
        {...props}
      />
    </div>
  );
}

function CommandList({
  className,
  ...props
}: React.ComponentProps<typeof CommandPrimitive.List>) {
  return (
    <CommandPrimitive.List
      data-slot="command-list"
      className={cn(
        "scroll-gradient max-h-[400px] scroll-py-2 overflow-x-hidden overflow-y-auto p-2",
        className,
      )}
      {...props}
    />
  );
}

function CommandEmpty({
  ...props
}: React.ComponentProps<typeof CommandPrimitive.Empty>) {
  return (
    <CommandPrimitive.Empty
      data-slot="command-empty"
      className="text-muted-foreground/70 py-8 text-center text-sm"
      {...props}
    />
  );
}

function CommandGroup({
  className,
  ...props
}: React.ComponentProps<typeof CommandPrimitive.Group>) {
  return (
    <CommandPrimitive.Group
      data-slot="command-group"
      className={cn(
        // Base styles
        "text-foreground overflow-hidden p-1",
        // Group heading styles with gradient
        "[&_[cmdk-group-heading]]:text-muted-foreground/80 [&_[cmdk-group-heading]]:px-3 [&_[cmdk-group-heading]]:py-2",
        "[&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-semibold [&_[cmdk-group-heading]]:uppercase",
        "[&_[cmdk-group-heading]]:bg-gradient-to-r [&_[cmdk-group-heading]]:tracking-wider",
        "[&_[cmdk-group-heading]]:from-fuchsia-500/10 [&_[cmdk-group-heading]]:to-cyan-400/10",
        "[&_[cmdk-group-heading]]:mb-1 [&_[cmdk-group-heading]]:rounded-md",
        className,
      )}
      {...props}
    />
  );
}

function CommandSeparator({
  className,
  ...props
}: React.ComponentProps<typeof CommandPrimitive.Separator>) {
  return (
    <CommandPrimitive.Separator
      data-slot="command-separator"
      className={cn(
        "via-border/50 -mx-1 my-2 h-px bg-gradient-to-r from-transparent to-transparent",
        className,
      )}
      {...props}
    />
  );
}

function CommandItem({
  className,
  ...props
}: React.ComponentProps<typeof CommandPrimitive.Item>) {
  return (
    <CommandPrimitive.Item
      data-slot="command-item"
      className={cn(
        // Base styles
        "relative flex cursor-default items-center gap-3 rounded-lg px-3 py-2.5 text-sm outline-hidden transition-all duration-200 select-none",
        // Selected/hover states with gradient
        "data-[selected=true]:bg-gradient-to-r data-[selected=true]:from-fuchsia-500/10 data-[selected=true]:to-cyan-400/10",
        "data-[selected=true]:text-foreground data-[selected=true]:scale-[1.02] data-[selected=true]:shadow-md",
        // Icon styles
        "[&_svg:not([class*='text-'])]:text-muted-foreground/70 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",
        "data-[selected=true]:[&_svg:not([class*='text-'])]:text-[var(--brand-primary)]",
        // Disabled states
        "data-[disabled=true]:bg-muted/20 data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50",
        // Hover enhancement - using CSS variables in Tailwind arbitrary values
        "hover:bg-[linear-gradient(to_right,oklch(from_var(--brand-primary)_l_c_h_/_0.05),oklch(from_var(--brand-secondary)_l_c_h_/_0.05))]",
        className,
      )}
      {...props}
    />
  );
}

function CommandShortcut({
  className,
  ...props
}: React.ComponentProps<"span">) {
  return (
    <span
      data-slot="command-shortcut"
      className={cn(
        "text-muted-foreground/60 ml-auto text-xs font-medium tracking-wider",
        "from-muted/30 to-muted/20 border-border/30 rounded-md border bg-gradient-to-r px-2 py-1",
        className,
      )}
      {...props}
    />
  );
}

export {
  Command,
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
  CommandShortcut,
};
