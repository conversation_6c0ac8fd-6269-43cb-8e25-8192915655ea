export interface GetConstantsResponse {
  status_code: number;
  status: string;
  message: null;
  data: ConstantsData;
}

export interface ConstantsData {
  business: AreasType[];
  devices: AreasType[];
  engine_hours: AreasType[];
  fuels: AreasType[];
  user_types: AreasType[];
  areas_types: AreasType[];
}

export interface AreasType {
  id: number;
  name: string;
  title: string;
}

export interface GetModelsResponse {
  status_code: number;
  status: string;
  message: null;
  data: ModelsData;
}

export interface ModelsData {
  Protocol: Model[];
  Driver: Model[];
  Trailer: Model[];
  Permission: Permission[];
}

export interface Model {
  id: number;
  name: string;
  is_active: boolean;
}

export interface Permission {
  id: number;
  name: string;
}
