import { constantsAtom } from "@/atoms/app/constants-atom";
import { openCreateUnitAtom } from "@/atoms/app/open-atoms";
import { unitsAtom } from "@/atoms/entities/units-atom";
import { DataTable } from "@/components/table/data-table";
import ConfirmDeleteUnitDialog from "@/components/units/confirm-delete-unit-dialog";
import CreateUnitDialog from "@/components/units/create-unit-dialog";
import EditUnitDialog from "@/components/units/edit-unit-dialog";
import { UnitColumns } from "@/components/units/unit-columns";
import GenericEntityPage from "@/components/utils/generic-entity-page";
import { useTranslation } from "react-i18next";

export default function UnitsPage() {
  const { t } = useTranslation();
  const { units } = unitsAtom.useValue();

  return (
    <GenericEntityPage
      title="units.header.title"
      addText="units.header.add"
      onAddClick={openCreateUnitAtom.open}
      leftContent={<DataTable columns={UnitColumns(t)} data={units} />}
      dialogs={
        <>
          <ConfirmDeleteUnitDialog />
          <CreateUnitDialog />
          <EditUnitDialog />
        </>
      }
      fetchers={[unitsAtom.getUnits, constantsAtom.getConstants]}
    />
  );
}
