"use client";

import { ChevronRight, type LucideIcon } from "lucide-react";
import * as React from "react";

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@/components/ui/sidebar";
import { cn } from "@/lib/utils";
import i18n from "@/localization/i18n";
import { useTranslation } from "react-i18next";
import { Link, useLocation, useNavigate } from "react-router";

export function NavMain({
  items,
  searchQuery = "",
  isCollapsed = false,
  onNavigate,
}: {
  items: {
    title: string;
    url: string;
    icon?: LucideIcon;
    isActive?: boolean;
    badge?: string;
    items?: {
      title: string;
      url: string;
    }[];
  }[];
  searchQuery?: string;
  isCollapsed?: boolean;
  onNavigate?: () => void;
}) {
  const navigate = useNavigate();
  const { pathname } = useLocation();
  const { t } = useTranslation();

  // Filter items based on search query
  const filteredItems = React.useMemo(() => {
    if (!searchQuery) return items;

    return items.filter((item) => {
      const matchesTitle = item.title
        .toLowerCase()
        .includes(searchQuery.toLowerCase());
      const matchesSubItems = item.items?.some((subItem) =>
        subItem.title.toLowerCase().includes(searchQuery.toLowerCase()),
      );
      return matchesTitle || matchesSubItems;
    });
  }, [items, searchQuery]);

  return (
    <SidebarGroup className="p-0">
      {!isCollapsed && (
        <SidebarGroupLabel className="text-muted-foreground px-2 text-xs font-semibold">
          {t("ui.sidebar.navigation")}
        </SidebarGroupLabel>
      )}
      <SidebarMenu className="gap-1">
        {filteredItems.map((item) => (
          <div key={item.title}>
            {item.items ? (
              <Collapsible
                asChild
                defaultOpen={item.isActive || Boolean(searchQuery)}
                className="group/collapsible"
              >
                <SidebarMenuItem>
                  <CollapsibleTrigger asChild>
                    <SidebarMenuButton
                      tooltip={item.title}
                      className={cn(
                        "group hover:bg-accent/50 relative h-10 cursor-pointer rounded-xl transition-all duration-200",
                        "data-[state=open]:bg-accent/30",
                      )}
                    >
                      {item.icon && (
                        <item.icon className="text-muted-foreground group-hover:text-foreground h-4 w-4" />
                      )}
                      <span className="font-medium">{item.title}</span>
                      {item.badge && (
                        <span className="ml-auto rounded-full bg-gradient-to-r from-fuchsia-500 to-cyan-400 px-2 py-0.5 text-xs font-semibold text-white shadow-sm">
                          {item.badge}
                        </span>
                      )}
                      <ChevronRight
                        className={cn(
                          "text-muted-foreground ms-auto h-4 w-4 transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90",
                          i18n.language === "ar" && "rotate-180",
                          item.badge && "ml-1",
                        )}
                      />
                    </SidebarMenuButton>
                  </CollapsibleTrigger>
                  <CollapsibleContent className="data-[state=closed]:animate-collapsible-up data-[state=open]:animate-collapsible-down overflow-hidden">
                    <SidebarMenuSub className="border-border/50 ml-4 border-l pl-4">
                      {item.items
                        ?.filter(
                          (subItem) =>
                            !searchQuery ||
                            subItem.title
                              .toLowerCase()
                              .includes(searchQuery.toLowerCase()),
                        )
                        .map((subItem) => (
                          <SidebarMenuSubItem key={subItem.title}>
                            <SidebarMenuSubButton
                              asChild
                              className={cn(
                                "h-9 rounded-lg transition-all duration-200",
                                pathname === subItem.url &&
                                  "bg-gradient-to-r from-cyan-400/10 to-fuchsia-500/10 font-medium text-cyan-500 ring-1 ring-cyan-400/20",
                              )}
                            >
                              <Link
                                to={subItem.url}
                                className="flex items-center gap-2"
                              >
                                <div className="bg-muted-foreground/50 h-1.5 w-1.5 rounded-full" />
                                <span>{subItem.title}</span>
                              </Link>
                            </SidebarMenuSubButton>
                          </SidebarMenuSubItem>
                        ))}
                    </SidebarMenuSub>
                  </CollapsibleContent>
                </SidebarMenuItem>
              </Collapsible>
            ) : (
              <SidebarMenuItem>
                <SidebarMenuButton
                  tooltip={item.title}
                  onClick={() => {
                    navigate(item.url);
                    onNavigate?.();
                  }}
                  className={cn(
                    "group relative h-10 cursor-pointer rounded-xl transition-all duration-200",
                    pathname === item.url ? "font-medium ring-1" : "",
                  )}
                  style={
                    pathname === item.url
                      ? {
                          background: `linear-gradient(to right,
                            oklch(from var(--brand-secondary) l c h / 0.1),
                            oklch(from var(--brand-primary) l c h / 0.1))`,
                          color: "var(--brand-secondary)",
                          borderColor:
                            "oklch(from var(--brand-secondary) l c h / 0.2)",
                        }
                      : {
                          background: `linear-gradient(to right,
                            oklch(from var(--brand-secondary) l c h / 0.05),
                            oklch(from var(--brand-primary) l c h / 0.05))`,
                        }
                  }
                >
                  {item.icon && (
                    <item.icon
                      className={cn(
                        "h-4 w-4 transition-colors",
                        pathname === item.url
                          ? ""
                          : "text-muted-foreground group-hover:text-foreground",
                      )}
                      style={
                        pathname === item.url
                          ? { color: "var(--brand-secondary)" }
                          : {}
                      }
                    />
                  )}
                  <span className="font-medium">{item.title}</span>
                  {item.badge && (
                    <span
                      className="ms-auto rounded-full px-2 py-0.5 text-xs font-semibold text-white shadow-sm"
                      style={{
                        background: `linear-gradient(to right, var(--brand-primary), var(--brand-secondary))`,
                      }}
                    >
                      {item.badge}
                    </span>
                  )}
                </SidebarMenuButton>
              </SidebarMenuItem>
            )}
          </div>
        ))}
      </SidebarMenu>

      {/* No results message */}
      {searchQuery && filteredItems.length === 0 && (
        <div className="px-2 py-4 text-center">
          <p className="text-muted-foreground text-sm">
            {t("ui.sidebar.noResultsFound")}
          </p>
          <p className="text-muted-foreground/70 text-xs">
            {t("ui.sidebar.tryDifferentSearch")}
          </p>
        </div>
      )}
    </SidebarGroup>
  );
}
