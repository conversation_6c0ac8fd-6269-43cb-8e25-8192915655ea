import { constantsAtom } from "@/atoms/app/constants-atom";
import { models<PERSON>tom } from "@/atoms/app/models-atom";
import { openUpdateUnitAtom } from "@/atoms/app/open-atoms";
import { selectedUnitAtom } from "@/atoms/app/selected-atoms";
import { unitsAtom } from "@/atoms/entities/units-atom";
import type { UpdateUnitFormData } from "@/types/units";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { z } from "zod";

export default function useEditUnitDialog() {
  const { t } = useTranslation();

  // Input schema (all strings, password optional)
  const FormInputSchema = z.object({
    name: z.string().nonempty(t("units.validation.nameRequired")),
    plate_number: z
      .string()
      .nonempty(t("units.validation.plateNumberRequired")),
    device_type: z.string().nonempty(t("units.validation.deviceTypeRequired")),
    device_serial_number: z
      .string()
      .nonempty(t("units.validation.deviceSerialNumberRequired")),
    sim_card_number: z
      .string()
      .nonempty(t("units.validation.simCardNumberRequired")),
    sim_card_serial_number: z
      .string()
      .nonempty(t("units.validation.simCardSerialNumberRequired")),
    imei: z.string().nonempty(t("units.validation.imeiRequired")).max(15),
    password: z.string().optional(), // ✅ optional now
    operation_code: z
      .string()
      .nonempty(t("units.validation.operationCodeRequired")),
    engine_hours_type: z
      .string()
      .nonempty(t("units.validation.engineHoursTypeRequired")),
    engine_hours_value: z
      .string()
      .nonempty(t("units.validation.engineHoursValueRequired")),
    odometer_type: z
      .string()
      .nonempty(t("units.validation.odometerTypeRequired")),
    odometer_val: z
      .string()
      .nonempty(t("units.validation.odometerValRequired")),
    protocol_id: z.string().nonempty(t("units.validation.protocolIdRequired")),
    business_type: z
      .string()
      .nonempty(t("units.validation.businessTypeRequired")),
    vehicle_type: z
      .string()
      .nonempty(t("units.validation.vehicleTypeRequired")),
    measurement_type: z
      .string()
      .nonempty(t("units.validation.measurementTypeRequired")),
    max_capacity: z
      .string()
      .nonempty(t("units.validation.maxCapacityRequired")),
    seats: z.string().nonempty(t("units.validation.seatsRequired")),
  });

  // Output schema with transformations
  const FormOutputSchema = FormInputSchema.transform((data) => {
    const transformed = {
      ...data,
      device_type: Number(data.device_type),
      engine_hours_type: Number(data.engine_hours_type),
      engine_hours_value: Number(data.engine_hours_value),
      odometer_type: Number(data.odometer_type),
      odometer_val: Number(data.odometer_val),
      protocol_id: Number(data.protocol_id),
      business_type: Number(data.business_type),
      measurement_type: Number(data.measurement_type),
      max_capacity: Number(data.max_capacity),
      seats: Number(data.seats),
    };

    // ✅ remove password if not provided or empty string
    if (!data.password) {
      delete transformed.password;
    }

    return transformed;
  });

  type FormInput = z.infer<typeof FormInputSchema>;

  const form = useForm<FormInput>({
    resolver: zodResolver(FormInputSchema), // Use input schema for validation
    defaultValues: {
      name: "",
      plate_number: "",
      device_type: "",
      device_serial_number: "",
      sim_card_number: "",
      sim_card_serial_number: "",
      imei: "",
      password: "",
      operation_code: "",
      engine_hours_type: "",
      engine_hours_value: "",
      odometer_type: "",
      odometer_val: "",
      protocol_id: "",
      business_type: "",
      vehicle_type: "",
      measurement_type: "",
      max_capacity: "",
      seats: "",
    },
  });

  const { plate_number } = selectedUnitAtom.useValue();
  const { oneUnit } = unitsAtom.useValue();

  const isOpened = openUpdateUnitAtom.useOpened();
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingFetchUnit, setIsLoadingFetchUnit] = useState(false);

  const { constants } = constantsAtom.useValue();
  const { models } = modelsAtom.useValue();

  async function onSubmit(inputData: FormInput) {
    // Parse and transform data using output schema
    const result = FormOutputSchema.safeParse(inputData);

    if (!result.success) {
      // Handle validation errors if needed
      return;
    }

    const formData: UpdateUnitFormData = result.data;

    setIsLoading(true);

    if (plate_number) {
      await unitsAtom.editUnit(plate_number, formData, () => {
        closeDialog();
      });
    }

    setIsLoading(false);
  }

  const closeDialog = () => {
    openUpdateUnitAtom.close();
    selectedUnitAtom.reset();
    unitsAtom.change("oneUnit", null);
  };

  useEffect(() => {
    async function fetchUnit() {
      if (plate_number) {
        setIsLoadingFetchUnit(true);
        await unitsAtom.getOneUnit(plate_number);
        setIsLoadingFetchUnit(false);
      }
    }

    fetchUnit();
  }, [plate_number]);

  useEffect(() => {
    if (oneUnit) {
      form.reset({
        name: oneUnit.name,
        plate_number: oneUnit.plate_number,
        // device_type: String(oneUnit.device_type),
        device_serial_number: oneUnit.device_serial_number,
        sim_card_number: oneUnit.sim_card_number,
        // sim_card_serial_number: oneUnit.sim_card_serial_number,
        imei: oneUnit.imei,
        // password: oneUnit.password,
        operation_code: oneUnit.operation_code,
        engine_hours_type: String(oneUnit.engine_hours_type.id),
        engine_hours_value: String(oneUnit.engine_hours_value),
        odometer_type: String(oneUnit.odometer_type.id),
        odometer_val: String(oneUnit.odometer_val),
        protocol_id: String(oneUnit.protocol.id),
        // business_type: String(oneUnit.business_type),
        // vehicle_type: String(oneUnit.vehicle_type),
        // measurement_type: String(oneUnit.measurement_type),
        // max_capacity: String(oneUnit.max_capacity),
        seats: String(oneUnit.details.seats),
      });
    }
  }, [form, oneUnit]);

  return {
    form,
    isOpened,
    isLoading,
    isLoadingFetchUnit,
    onSubmit,
    closeDialog,
    t,
    constants,
    models,
  };
}
