import { openCreateTrailerAtom } from "@/atoms/app/open-atoms";
import { trailresAtom } from "@/atoms/entities/trailers-atom";
import { DataTable } from "@/components/table/data-table";
import ConfirmDeleteTrailerDialog from "@/components/trailers/confirm-delete-trailer-dialog";
import CreateTrailerDialog from "@/components/trailers/create-trailer-dialog";
import EditTrailerDialog from "@/components/trailers/edit-trailer-dialog";
import { TrailerColumns } from "@/components/trailers/trailer-columns";
import GenericEntityPage from "@/components/utils/generic-entity-page";
import { useTranslation } from "react-i18next";

export default function TrailersPage() {
  const { t } = useTranslation();
  const { trailers } = trailresAtom.useValue();

  return (
    <GenericEntityPage
      title="trailers.title"
      addText="trailers.add"
      onAddClick={openCreateTrailerAtom.open}
      leftContent={<DataTable columns={TrailerColumns(t)} data={trailers} />}
      dialogs={
        <>
          <CreateTrailerDialog />
          <EditTrailerDialog />
          <ConfirmDeleteTrailerDialog />
        </>
      }
      fetchers={[trailresAtom.getTrailers]}
    />
  );
}
