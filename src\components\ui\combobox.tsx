"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { CheckIcon, ChevronDownIcon, XIcon } from "lucide-react";
import * as React from "react";

export interface ComboboxOption {
  value: string;
  label: string;
  disabled?: boolean;
}

interface ComboboxProps {
  options: ComboboxOption[];
  value?: string;
  onValueChange?: (value: string) => void;
  placeholder?: string;
  searchPlaceholder?: string;
  emptyText?: string;
  className?: string;
  disabled?: boolean;
  clearable?: boolean;
  size?: "sm" | "default" | "lg";
}

export function Combobox({
  options,
  value,
  onValueChange,
  placeholder = "Select option...",
  searchPlaceholder = "Search options...",
  emptyText = "No options found.",
  className,
  disabled = false,
  clearable = false,
  size = "default",
}: ComboboxProps) {
  const [open, setOpen] = React.useState(false);
  const [searchValue, setSearchValue] = React.useState("");

  const selectedOption = options.find((option) => option.value === value);

  const filteredOptions = options.filter((option) =>
    option.label.toLowerCase().includes(searchValue.toLowerCase()),
  );

  const handleSelect = (selectedValue: string) => {
    if (selectedValue === value) {
      onValueChange?.("");
    } else {
      onValueChange?.(selectedValue);
    }
    setOpen(false);
    setSearchValue("");
  };

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    onValueChange?.("");
  };

  const sizeClasses = {
    sm: "h-8 px-3 text-xs",
    default: "h-10 px-4 text-sm",
    lg: "h-12 px-6 text-base",
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          disabled={disabled}
          className={cn(
            // Base styles
            "w-full justify-between rounded-xl font-normal transition-all duration-300",
            // Size variants
            sizeClasses[size],
            // Glass morphism styling
            "border-white/10 bg-gradient-to-br from-slate-900/95 via-slate-800/90 to-slate-900/95 backdrop-blur-2xl",
            "hover:border-white/20 hover:bg-gradient-to-br hover:from-slate-800/95 hover:via-slate-700/90 hover:to-slate-800/95 hover:shadow-md",
            "focus:border-cyan-400/50 focus:bg-gradient-to-br focus:from-slate-800/95 focus:via-slate-700/90 focus:to-slate-800/95",
            "focus:shadow-lg focus:ring-2 focus:shadow-cyan-400/10 focus:ring-cyan-400/20",
            // Selected state
            selectedOption &&
              "border-cyan-400/30 bg-gradient-to-br from-cyan-400/5 to-blue-500/5",
            className,
          )}
        >
          <span
            className={cn(
              "truncate text-slate-200",
              !selectedOption && "text-slate-400",
            )}
          >
            {selectedOption ? selectedOption.label : placeholder}
          </span>
          <div className="flex items-center gap-1">
            {clearable && selectedOption && (
              <XIcon
                className="h-4 w-4 cursor-pointer text-slate-400 transition-colors hover:text-slate-200"
                onClick={handleClear}
              />
            )}
            <ChevronDownIcon
              className={cn(
                "h-4 w-4 text-slate-400 transition-transform duration-300",
                open && "rotate-180",
              )}
            />
          </div>
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className="w-[var(--radix-popover-trigger-width)] rounded-xl border-white/10 bg-gradient-to-br from-slate-900/95 via-slate-800/90 to-slate-900/95 p-0 shadow-2xl backdrop-blur-2xl"
        align="start"
      >
        <Command className="border-0">
          <CommandInput
            placeholder={searchPlaceholder}
            value={searchValue}
            onValueChange={setSearchValue}
          />
          <CommandList>
            <CommandEmpty className="py-6 text-center">
              <div className="text-muted-foreground/70 flex flex-col items-center gap-2">
                <div className="text-sm font-medium">{emptyText}</div>
                {searchValue && (
                  <div className="text-xs">Try adjusting your search term</div>
                )}
              </div>
            </CommandEmpty>
            <CommandGroup>
              {filteredOptions.map((option) => (
                <CommandItem
                  key={option.value}
                  value={option.value}
                  disabled={option.disabled}
                  onSelect={() => handleSelect(option.value)}
                  className={cn(
                    "cursor-pointer",
                    option.disabled && "cursor-not-allowed",
                  )}
                >
                  <CheckIcon
                    className={cn(
                      "h-4 w-4 transition-all duration-200",
                      value === option.value
                        ? "scale-110 text-fuchsia-500 opacity-100"
                        : "scale-75 opacity-0",
                    )}
                  />
                  <span
                    className={cn(
                      "truncate",
                      option.disabled && "text-muted-foreground/50",
                    )}
                  >
                    {option.label}
                  </span>
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}

// Multi-select combobox variant
interface MultiComboboxProps
  extends Omit<ComboboxProps, "value" | "onValueChange"> {
  values?: string[];
  onValuesChange?: (values: string[]) => void;
  maxSelected?: number;
}

export function MultiCombobox({
  options,
  values = [],
  onValuesChange,
  placeholder = "Select options...",
  searchPlaceholder = "Search options...",
  emptyText = "No options found.",
  className,
  disabled = false,
  size = "default",
  maxSelected,
}: MultiComboboxProps) {
  const [open, setOpen] = React.useState(false);
  const [searchValue, setSearchValue] = React.useState("");

  const selectedOptions = options.filter((option) =>
    values.includes(option.value),
  );
  const filteredOptions = options.filter((option) =>
    option.label.toLowerCase().includes(searchValue.toLowerCase()),
  );

  const handleSelect = (selectedValue: string) => {
    const newValues = values.includes(selectedValue)
      ? values.filter((v) => v !== selectedValue)
      : maxSelected && values.length >= maxSelected
        ? values
        : [...values, selectedValue];

    onValuesChange?.(newValues);
    setSearchValue("");
  };

  const handleRemove = (valueToRemove: string, e: React.MouseEvent) => {
    e.stopPropagation();
    onValuesChange?.(values.filter((v) => v !== valueToRemove));
  };

  const sizeClasses = {
    sm: "min-h-8 px-3 text-xs",
    default: "min-h-10 px-4 text-sm",
    lg: "min-h-12 px-6 text-base",
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          disabled={disabled}
          className={cn(
            // Base styles
            "w-full justify-between font-normal transition-all duration-200",
            // Size variants
            sizeClasses[size],
            // Enhanced styling
            "border-border/50 bg-background/50 backdrop-blur-sm",
            "hover:bg-background/70 hover:border-fuchsia-500/30 hover:shadow-md",
            "focus:bg-background/80 focus:border-transparent",
            "focus:ring-2 focus:ring-fuchsia-500/30",
            "focus:shadow-lg focus:shadow-fuchsia-500/10",
            // Selected state
            selectedOptions.length > 0 &&
              "border-fuchsia-500/50 bg-gradient-to-r from-fuchsia-500/5 to-cyan-400/5",
            className,
          )}
        >
          <div className="flex flex-1 flex-wrap gap-1">
            {selectedOptions.length === 0 ? (
              <span className="text-muted-foreground/70">{placeholder}</span>
            ) : (
              selectedOptions.map((option) => (
                <span
                  key={option.value}
                  className="text-foreground border-border/30 inline-flex items-center gap-1 rounded-md border bg-gradient-to-r from-fuchsia-500/10 to-cyan-400/10 px-2 py-1 text-xs font-medium"
                >
                  {option.label}
                  <XIcon
                    className="hover:text-destructive h-3 w-3 cursor-pointer transition-colors"
                    onClick={(e) => handleRemove(option.value, e)}
                  />
                </span>
              ))
            )}
          </div>
          <ChevronDownIcon
            className={cn(
              "text-muted-foreground/70 ml-2 h-4 w-4 shrink-0 transition-transform duration-200",
              open && "rotate-180",
            )}
          />
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className="border-border/50 w-[var(--radix-popover-trigger-width)] p-0 shadow-xl backdrop-blur-sm"
        align="start"
      >
        <Command className="border-0">
          <CommandInput
            placeholder={searchPlaceholder}
            value={searchValue}
            onValueChange={setSearchValue}
          />
          <CommandList>
            <CommandEmpty className="py-6 text-center">
              <div className="text-muted-foreground/70 flex flex-col items-center gap-2">
                <div className="text-sm font-medium">{emptyText}</div>
                {searchValue && (
                  <div className="text-xs">Try adjusting your search term</div>
                )}
              </div>
            </CommandEmpty>
            <CommandGroup>
              {maxSelected && (
                <div className="text-muted-foreground/70 mb-1 rounded-md bg-gradient-to-r from-fuchsia-500/5 to-cyan-400/5 px-3 py-2 text-xs">
                  {values.length} of {maxSelected} selected
                </div>
              )}
              {filteredOptions.map((option) => {
                const isSelected = values.includes(option.value);
                const isDisabled =
                  option.disabled ||
                  Boolean(
                    maxSelected && !isSelected && values.length >= maxSelected,
                  );

                return (
                  <CommandItem
                    key={option.value}
                    value={option.value}
                    disabled={isDisabled}
                    onSelect={() => handleSelect(option.value)}
                    className={cn(
                      "cursor-pointer",
                      isDisabled && "cursor-not-allowed",
                    )}
                  >
                    <CheckIcon
                      className={cn(
                        "h-4 w-4 transition-all duration-200",
                        isSelected
                          ? "scale-110 opacity-100"
                          : "scale-75 opacity-0",
                      )}
                      style={
                        isSelected ? { color: "var(--brand-primary)" } : {}
                      }
                    />
                    <span
                      className={cn(
                        "truncate",
                        isDisabled && "text-muted-foreground/50",
                      )}
                    >
                      {option.label}
                    </span>
                  </CommandItem>
                );
              })}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
