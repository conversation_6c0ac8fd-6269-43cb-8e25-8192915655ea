import { openCreateUserAtom } from "@/atoms/app/open-atoms";
import { usersAtom } from "@/atoms/entities/users-atom";
import { DataTable } from "@/components/table/data-table";
import ConfirmDeleteUserDialog from "@/components/users/confirm-delete-user-dialog";
import CreateUserDialog from "@/components/users/create-user-dialog";
import EditUserDialog from "@/components/users/edit-user-dialog";
import { UserColumns } from "@/components/users/user-columns";
import GenericEntityPage from "@/components/utils/generic-entity-page";
import { useTranslation } from "react-i18next";

export default function UsersPage() {
  const { t } = useTranslation();
  const { users } = usersAtom.useValue();

  return (
    <GenericEntityPage
      title="users.header.title"
      addText="users.header.add"
      onAddClick={openCreateUserAtom.open}
      leftContent={<DataTable columns={UserColumns(t)} data={users} />}
      dialogs={
        <>
          <ConfirmDeleteUserDialog />
          <CreateUserDialog />
          <EditUserDialog />
        </>
      }
      fetchers={[usersAtom.getUsers]}
    />
  );
}
