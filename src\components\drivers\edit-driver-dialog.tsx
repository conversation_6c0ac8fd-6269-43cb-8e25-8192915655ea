import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogScrollArea,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import useEditDriverDialog from "@/hooks/drivers/use-edit-driver-dialog";
import { cn } from "@/lib/utils";
import i18n from "@/localization/i18n";
import { Check, ChevronsUpDown, Loader2 } from "lucide-react";
import { OverlayLoader } from "../utils/overlay-loader";

export default function EditDriverDialog() {
  const {
    form,
    isLoading,
    isOpened,
    closeDialog,
    onSubmit,
    isLoadingFetchDriver,
    t,
    driverTypes,
  } = useEditDriverDialog();

  return (
    <Dialog open={isOpened} onOpenChange={closeDialog}>
      <DialogContent dir={i18n.language === "ar" ? "rtl" : "ltr"}>
        <DialogHeader>
          <DialogTitle>{t("drivers.dialog.editTitle")}</DialogTitle>
          <DialogDescription>
            {t("drivers.dialog.editDescription")}
          </DialogDescription>
        </DialogHeader>

        {isLoadingFetchDriver ? (
          <OverlayLoader inCenter={false} />
        ) : (
          <>
            <DialogScrollArea>
              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(onSubmit)}
                  className="space-y-6"
                >
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t("drivers.form.name")}</FormLabel>
                          <FormControl>
                            <Input
                              placeholder={t("drivers.form.namePlaceholder")}
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="driver_type"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>{t("drivers.form.driverType")}</FormLabel>
                          <Popover>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  variant="outline"
                                  role="combobox"
                                  className={cn(
                                    "dark:bg-input/20 dark:border-input/30 w-full justify-between",
                                    !field.value && "text-muted-foreground",
                                  )}
                                >
                                  {field.value
                                    ? driverTypes.find(
                                        (d) => d.id === +field.value,
                                      )?.label
                                    : t("drivers.form.driverTypePlaceholder")}
                                  <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0">
                              <Command>
                                <CommandInput
                                  placeholder={t(
                                    "drivers.form.driverTypePlaceholder",
                                  )}
                                  className="h-9"
                                />
                                <CommandList>
                                  <CommandEmpty>
                                    {t("drivers.form.no_driver_type_found")}
                                  </CommandEmpty>
                                  <CommandGroup>
                                    {driverTypes.map((type) => (
                                      <CommandItem
                                        key={type.id}
                                        value={type.label}
                                        onSelect={() => {
                                          field.onChange(type.id);
                                        }}
                                      >
                                        <Check
                                          className={cn(
                                            "mr-2 h-4 w-4",
                                            type.id === +field.value
                                              ? "opacity-100"
                                              : "opacity-0",
                                          )}
                                        />
                                        {type.label}
                                      </CommandItem>
                                    ))}
                                  </CommandGroup>
                                </CommandList>
                              </Command>
                            </PopoverContent>
                          </Popover>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="national_id"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t("drivers.form.nationalId")}</FormLabel>
                          <FormControl>
                            <Input
                              placeholder={t(
                                "drivers.form.nationalIdPlaceholder",
                              )}
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="nationality"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t("drivers.form.nationality")}</FormLabel>
                          <FormControl>
                            <Input
                              placeholder={t(
                                "drivers.form.nationalityPlaceholder",
                              )}
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="identity_number"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            {t("drivers.form.identityNumber")}
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder={t(
                                "drivers.form.identityNumberPlaceholder",
                              )}
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="address"
                      render={({ field }) => (
                        <FormItem className="md:col-span-2">
                          <FormLabel>{t("drivers.form.address")}</FormLabel>
                          <Textarea
                            placeholder={t("drivers.form.addressPlaceholder")}
                            className="resize-none"
                            {...field}
                          />
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="is_active"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">
                            {t("drivers.form.isActive")}
                          </FormLabel>
                          <p className="text-muted-foreground text-sm">
                            {t("drivers.form.isActiveDescription")}
                          </p>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </form>
              </Form>
            </DialogScrollArea>

            <DialogFooter>
              <DialogClose asChild>
                <Button
                  variant="gradient-outline"
                  type="button"
                  disabled={isLoading}
                  size="lg"
                >
                  {t("drivers.actions.cancel")}
                </Button>
              </DialogClose>
              <Button
                variant="gradient"
                type="submit"
                disabled={isLoading}
                size="lg"
                onClick={form.handleSubmit(onSubmit)}
              >
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isLoading
                  ? t("drivers.actions.updating")
                  : t("drivers.actions.update")}
              </Button>
            </DialogFooter>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
}
