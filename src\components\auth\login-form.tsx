import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { Eye, EyeOff, Loader2 } from "lucide-react";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { Link } from "react-router";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import useLoginForm from "@/hooks/auth/use-login-form";
import { AppleIcon, FacebookIcon, GoogleIcon } from "./social-login-icons";

export function LoginForm({
  className,
  ...props
}: React.ComponentProps<"div">) {
  const { form, onSubmit, loading } = useLoginForm();
  const { t } = useTranslation();
  const [showPassword, setShowPassword] = useState(false);

  return (
    <div className={cn("grid gap-8", className)} {...props}>
      <div className="flex flex-col space-y-3 text-center">
        <h1 className="bg-gradient-to-r from-fuchsia-500 to-cyan-400 bg-clip-text text-4xl font-extrabold text-transparent">
          {t("auth.welcome_back")}
        </h1>
        <p className="text-muted-foreground text-base">
          {t("auth.login_to_account")}
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-base font-semibold">
                  {t("auth.email")}
                </FormLabel>
                <FormControl>
                  <Input
                    type="email"
                    placeholder={t("auth.enter_email")}
                    autoComplete="email"
                    className="h-12 text-base"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <div className="flex items-center justify-between">
                  <FormLabel className="text-base font-semibold">
                    {t("auth.password")}
                  </FormLabel>
                  <Link
                    to="#"
                    className="text-sm font-medium transition-colors hover:underline"
                    style={{
                      color: "var(--brand-primary)",
                      ":hover": { color: "var(--brand-secondary)" },
                    }}
                  >
                    {t("auth.forgotPassword")}
                  </Link>
                </div>
                <FormControl>
                  <div className="relative">
                    <Input
                      type={showPassword ? "text" : "password"}
                      placeholder={t("auth.enter_password")}
                      autoComplete="current-password"
                      className="h-12 pr-12 text-base"
                      {...field}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute top-0 right-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <EyeOff className="h-5 w-5" />
                      ) : (
                        <Eye className="h-5 w-5" />
                      )}
                      <span className="sr-only">
                        {showPassword ? "Hide password" : "Show password"}
                      </span>
                    </Button>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <Button
            type="submit"
            className="h-12 w-full bg-gradient-to-r from-fuchsia-500 to-cyan-400 text-base font-bold shadow-lg transition-all hover:scale-105 hover:shadow-xl"
            disabled={loading}
          >
            {loading && <Loader2 className="mr-2 h-5 w-5 animate-spin" />}
            {loading ? t("auth.logining") : t("auth.login")}
          </Button>
        </form>
      </Form>

      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <span className="w-full border-t" />
        </div>
        <div className="relative flex justify-center text-sm font-medium uppercase">
          <span className="bg-background text-muted-foreground px-3">
            {t("auth.or_continue_with")}
          </span>
        </div>
      </div>

      <div className="grid grid-cols-3 gap-4">
        <Button
          variant="outline"
          type="button"
          className="h-12 w-full transition-all hover:scale-105 hover:border-fuchsia-500/50 hover:bg-fuchsia-500/10"
        >
          <AppleIcon className="h-5 w-5" />
          <span className="sr-only">{t("auth.login_with_apple")}</span>
        </Button>

        <Button
          variant="outline"
          type="button"
          className="h-12 w-full transition-all hover:scale-105 hover:border-cyan-400/50 hover:bg-cyan-400/10"
        >
          <GoogleIcon className="h-5 w-5" />
          <span className="sr-only">{t("auth.login_with_google")}</span>
        </Button>

        <Button
          variant="outline"
          type="button"
          className="h-12 w-full transition-all hover:scale-105 hover:border-fuchsia-500/50 hover:bg-fuchsia-500/10"
        >
          <FacebookIcon className="h-5 w-5" />
          <span className="sr-only">{t("auth.login_with_meta")}</span>
        </Button>
      </div>

      <div className="text-muted-foreground text-center text-base">
        {t("auth.dont_have_account")}{" "}
        <Link
          to="#"
          className="bg-gradient-to-r from-fuchsia-500 to-cyan-400 bg-clip-text font-semibold text-transparent hover:underline"
        >
          {t("auth.sign_up")}
        </Link>
      </div>
    </div>
  );
}
