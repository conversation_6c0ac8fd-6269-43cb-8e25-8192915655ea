export interface GetAllTrailersResponse {
  status_code: number;
  status: string;
  message: null;
  data: Trailer[];
}

export interface Trailer {
  slug: string;
  name: string;
  description: string;
  is_active: boolean;
}

export interface CreateTrailerResponse {
  status_code: number;
  status: number;
  message: string;
  data: {
    name: string;
    description: string;
    is_active: boolean;
    owner_id: number;
    slug: string;
    updated_at: Date;
    created_at: Date;
    id: number;
  };
}

export interface CreateTrailerFormData {
  name: string;
  description: string;
  is_active: 0 | 1;
}

export interface UpdateTrailerResponse {
  status_code: number;
  status: string;
  message: string;
  data: Trailer;
}

export interface GetOneTrailerResponse {
  status_code: number;
  status: string;
  message: string;
  data: Trailer;
}

export interface DeleteTrailerResponse {
  status_code: number;
  status: string;
  message: null;
  data: string;
}
