import { useState } from "react";

export default function useTableResize() {
  const [tableSize, setTableSize] = useState(
    localStorage.getItem("gps-table-size")
      ? JSON.parse(localStorage.getItem("gps-table-size")!)
      : 50,
  );

  const handleResize = (e: number) => {
    setTableSize(e);
    localStorage.setItem("gps-table-size", JSON.stringify(e));
  };

  const minSize = 30;

  return {
    tableSize,
    handleResize,
    minSize,
  };
}
