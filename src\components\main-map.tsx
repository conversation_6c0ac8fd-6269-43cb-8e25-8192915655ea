import type { LatLngExpression } from "leaflet";
import { useEffect } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, Tile<PERSON><PERSON><PERSON>, useMap } from "react-leaflet";

const ResizeHandler = () => {
  const map = useMap();
  useEffect(() => {
    setTimeout(() => {
      map.invalidateSize();
    }, 100); // slight delay so container finishes rendering
  }, [map]);
  return null;
};

export default function MainMap() {
  const position: LatLngExpression = [26.8206, 30.8025];

  return (
    <div className="bg-background/80 h-full w-full overflow-hidden rounded-2xl shadow-lg">
      <MapContainer
        className="z-20 h-full w-full"
        center={position}
        zoom={6}
        scrollWheelZoom={false}
      >
        <ResizeHandler />
        <TileLayer
          className="h-full w-full"
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        />
      </MapContainer>
    </div>
  );
}
