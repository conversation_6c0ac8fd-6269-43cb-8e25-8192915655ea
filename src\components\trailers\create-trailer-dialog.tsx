import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  Dialog<PERSON>ooter,
  DialogHeader,
  DialogScrollArea,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import useCreateTrailerDialog from "@/hooks/trilers/use-create-trailer-dialog";
import i18n from "@/localization/i18n";
import { Button } from "../ui/button";
import { Switch } from "../ui/switch";
import { Textarea } from "../ui/textarea";

export default function CreateTrailerDialog() {
  const { closeDialog, form, isLoading, isOpened, onSubmit, t } =
    useCreateTrailerDialog();

  return (
    <Dialog open={isOpened} onOpenChange={closeDialog}>
      <DialogContent dir={i18n.language === "ar" ? "rtl" : "ltr"}>
        <DialogHeader>
          <DialogTitle>{t("trailers.create_dialog.title")}</DialogTitle>
          <DialogDescription>
            {t("trailers.create_dialog.description")}
          </DialogDescription>
        </DialogHeader>

        <DialogScrollArea>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("common.form.name")}</FormLabel>
                    <FormControl>
                      <Input
                        placeholder={t(
                          "trailers.create_dialog.form.name_placeholder",
                        )}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {t("trailers.create_dialog.form.description")}
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder={t(
                          "trailers.create_dialog.form.description_placeholder",
                        )}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="is_active"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-2xl border p-3 shadow">
                    <div className="space-y-0.5">
                      <FormLabel>
                        {t("trailers.create_dialog.form.is_active")}
                      </FormLabel>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </form>
          </Form>
        </DialogScrollArea>

        <DialogFooter>
          <DialogClose asChild>
            <Button variant="gradient-outline" type="button" size="lg">
              {t("trailers.create_dialog.actions.cancel")}
            </Button>
          </DialogClose>
          <Button
            variant="gradient"
            type="submit"
            disabled={isLoading}
            size="lg"
            onClick={form.handleSubmit(onSubmit)}
          >
            {isLoading
              ? t("trailers.create_dialog.actions.creating")
              : t("trailers.create_dialog.actions.create")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
