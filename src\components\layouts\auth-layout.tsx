import { URLS } from "@/utils/urls";
import { Link, Outlet } from "react-router";
import { FloatingSidebar } from "../auth/floating-sidebar";

export default function AuthLayout() {
  return (
    <div className="via-background/80 min-h-screen bg-gradient-to-br from-fuchsia-500/10 to-cyan-400/10">
      <FloatingSidebar />

      <div className="relative container grid min-h-screen flex-col items-center justify-center lg:max-w-none lg:grid-cols-[1fr_2fr] lg:px-0">
        {/* Left side - Branding (smaller) */}
        <div className="relative hidden h-full flex-col p-6 text-white lg:flex">
          <div className="absolute inset-0 rounded-r-3xl bg-gradient-to-br from-fuchsia-500/90 to-cyan-400/90" />
          <div className="relative z-20 flex items-center text-base font-semibold">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="mr-2 h-5 w-5 animate-bounce"
            >
              <path d="m8 3 4 8 5-5v11H5V6l3-3z" />
            </svg>
            <span className="bg-gradient-to-r from-white to-white/80 bg-clip-text font-bold text-transparent">
              Your App
            </span>
          </div>
          <div className="relative z-20 mt-auto">
            <blockquote className="space-y-3">
              <p className="text-base leading-relaxed font-medium">
                "Powerful features that transform operations with intuitive
                design."
              </p>
              <footer className="text-xs font-medium opacity-90">
                — Sofia Davis
              </footer>
            </blockquote>
          </div>

          {/* Decorative elements */}
          <div className="absolute top-16 right-6 h-12 w-12 animate-pulse rounded-full bg-white/10" />
          <div className="absolute right-12 bottom-24 h-8 w-8 animate-bounce rounded-full bg-white/5" />
          <div className="absolute top-1/2 right-3 h-4 w-4 rounded-full bg-white/20" />
        </div>

        {/* Right side - Auth form (bigger) */}
        <div className="flex min-h-screen items-center justify-center lg:p-12">
          <div className="mx-auto flex w-full flex-col justify-center space-y-8 p-8 sm:w-[500px]">
            <div className="from-background/95 to-background/90 rounded-3xl border-0 bg-gradient-to-br p-10 shadow-2xl backdrop-blur-sm">
              <Outlet />
            </div>

            <p className="text-muted-foreground text-center text-sm">
              By clicking continue, you agree to our{" "}
              <Link
                to={URLS.termsOfService}
                className="underline underline-offset-4 transition-colors"
                style={{
                  color: "var(--link-color)",
                  ":hover": { color: "var(--brand-primary)" },
                }}
              >
                Terms of Service
              </Link>{" "}
              and{" "}
              <Link
                to={URLS.privacyPolicy}
                className="underline underline-offset-4 transition-colors"
                style={{
                  color: "var(--link-color)",
                  ":hover": { color: "var(--brand-secondary)" },
                }}
              >
                Privacy Policy
              </Link>
              .
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
