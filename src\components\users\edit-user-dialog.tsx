import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogScrollArea,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import useEditUserDialog from "@/hooks/users/use-edit-user-dialog";
import { cn } from "@/lib/utils";
import i18n from "@/localization/i18n";
import { Check, ChevronsUpDown } from "lucide-react";
import { Button } from "../ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "../ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";
import { OverlayLoader } from "../utils/overlay-loader";

export default function EditUserDialog() {
  const {
    form,
    isLoading,
    isLoadingFetchUser,
    isOpened,
    onSubmit,
    closeDialog,
    t,
  } = useEditUserDialog();

  return (
    <Dialog open={isOpened} onOpenChange={closeDialog}>
      <DialogContent dir={i18n.language === "ar" ? "rtl" : "ltr"}>
        <DialogHeader>
          <DialogTitle>{t("users.editUser.title")}</DialogTitle>
          <DialogDescription>
            {t("users.editUser.description")}
          </DialogDescription>
        </DialogHeader>

        {isLoadingFetchUser ? (
          <OverlayLoader inCenter={false} />
        ) : (
          <>
            <DialogScrollArea>
              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(onSubmit)}
                  className="space-y-6"
                >
                  {/* Row 1: Name + Email */}
                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            {t("users.createUser.fields.name")}
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder={t(
                                "users.createUser.placeholders.name",
                              )}
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            {t("users.createUser.fields.email")}
                          </FormLabel>
                          <FormControl>
                            <Input
                              type="email"
                              placeholder={t(
                                "users.createUser.placeholders.email",
                              )}
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    {/* Phone Code */}
                    <FormField
                      control={form.control}
                      name="phone_code"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>
                            {t("users.createUser.fields.phone_code")}
                          </FormLabel>
                          <Popover>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  variant="outline"
                                  role="combobox"
                                  className={cn(
                                    "dark:bg-input/20 dark:border-input/30 w-full justify-between",
                                    !field.value && "text-muted-foreground",
                                  )}
                                >
                                  {field.value
                                    ? t(
                                        `users.options.phoneCodes.${field.value}`,
                                      )
                                    : t(
                                        "users.createUser.placeholders.phone_code",
                                      )}
                                  <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0">
                              <Command>
                                <CommandInput
                                  placeholder={t(
                                    "users.createUser.placeholders.phone_code",
                                  )}
                                  className="h-9"
                                />
                                <CommandList>
                                  <CommandEmpty>
                                    {t("users.createUser.no_phone_code_found")}
                                  </CommandEmpty>
                                  <CommandGroup>
                                    {["966", "20", "971", "1"].map((code) => (
                                      <CommandItem
                                        key={code}
                                        value={code}
                                        onSelect={() => field.onChange(code)}
                                      >
                                        <Check
                                          className={cn(
                                            "mr-2 h-4 w-4",
                                            field.value === code
                                              ? "opacity-100"
                                              : "opacity-0",
                                          )}
                                        />
                                        {t(`users.options.phoneCodes.${code}`)}
                                      </CommandItem>
                                    ))}
                                  </CommandGroup>
                                </CommandList>
                              </Command>
                            </PopoverContent>
                          </Popover>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Phone */}
                    <FormField
                      control={form.control}
                      name="phone"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            {t("users.createUser.fields.phone")}
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder={t(
                                "users.createUser.placeholders.phone",
                              )}
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="lang"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>
                          {t("users.createUser.fields.lang")}
                        </FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant="outline"
                                role="combobox"
                                className={cn(
                                  "dark:bg-input/20 dark:border-input/30 w-full justify-between",
                                  !field.value && "text-muted-foreground",
                                )}
                              >
                                {field.value
                                  ? t(`users.options.languages.${field.value}`)
                                  : t("users.createUser.placeholders.lang")}
                                <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0">
                            <Command>
                              <CommandInput
                                placeholder={t(
                                  "users.createUser.placeholders.lang",
                                )}
                                className="h-9"
                              />
                              <CommandList>
                                <CommandEmpty>
                                  {t("users.createUser.no_lang_found")}
                                </CommandEmpty>
                                <CommandGroup>
                                  {["en", "ar"].map((lang) => (
                                    <CommandItem
                                      key={lang}
                                      value={lang}
                                      onSelect={() => field.onChange(lang)}
                                    >
                                      <Check
                                        className={cn(
                                          "mr-2 h-4 w-4",
                                          field.value === lang
                                            ? "opacity-100"
                                            : "opacity-0",
                                        )}
                                      />
                                      {t(`users.options.languages.${lang}`)}
                                    </CommandItem>
                                  ))}
                                </CommandGroup>
                              </CommandList>
                            </Command>
                          </PopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </form>
              </Form>
            </DialogScrollArea>

            <DialogFooter>
              <DialogClose asChild>
                <Button variant="gradient-outline" type="button" size="lg">
                  {t("common.actions.cancel")}
                </Button>
              </DialogClose>
              <Button
                variant="gradient"
                type="submit"
                disabled={isLoading}
                size="lg"
                onClick={form.handleSubmit(onSubmit)}
              >
                {isLoading
                  ? t("common.loading.updating")
                  : t("common.actions.update")}
              </Button>
            </DialogFooter>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
}
