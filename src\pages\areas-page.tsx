import { areasAtom } from "@/atoms/entities/areas-atom";
import { AreaColumns } from "@/components/areas/area-columns";
import { AreaMap } from "@/components/areas/area-map";
import ConfirmDeleteAreaDialog from "@/components/areas/confirm-delete-area-dialog";
import { DataTable } from "@/components/table/data-table";
import GenericEntityPage from "@/components/utils/generic-entity-page";
import { URLS } from "@/utils/urls";
import { useTranslation } from "react-i18next";

export default function AreasPage() {
  const { t } = useTranslation();
  const { areas } = areasAtom.useValue();

  return (
    <GenericEntityPage
      title="areas.title"
      addText="areas.add"
      addUrl={URLS.addArea}
      leftContent={<DataTable columns={AreaColumns(t)} data={areas} />}
      rightContent={<AreaMap />}
      dialogs={<ConfirmDeleteAreaDialog />}
      fetchers={[areasAtom.getAreas]}
    />
  );
}
