export const unitsTranslations = {
  header: {
    title: "Units",
    add: "Add Unit",
  },

  list: {
    title: "Units List",
    noData: "No units available",
    searchPlaceholder: "Search for a unit...",
    actions: {
      title: "Actions",
      create: "Create Unit",
      update: "Update",
      delete: "Delete",
      view: "View",
    },
    table: {
      selectAll: "Select all",
      selectRow: "Select row",
      driver: "Driver",
      details: {
        model: "Model",
        year: "Year",
        seats: "Seats",
      },
      isActive: "Is Active",
      actions: "Actions",
    },
  },

  create: {
    title: "Create Unit",
    description: "Fill in the details to create a new unit.",
  },

  update: {
    title: "Update Unit",
    description: "Update unit details.",
  },

  confirmDelete: {
    title: "Confirm Delete",
    description:
      "Are you sure you want to delete this unit? This action cannot be undone.",
    buttons: {
      confirm: "Yes, Delete",
    },
  },

  fields: {
    plate_number: "Plate Number",
    device_type: "Device Type",
    device_serial_number: "Device Serial Number",
    sim_card_number: "SIM Card Number",
    sim_card_serial_number: "SIM Card Serial Number",
    imei: "IMEI",
    icon: "Icon",
    operation_code: "Operation Code",
    engine_hours_type: "Engine Hours Type",
    engine_hours_value: "Engine Hours Value",
    odometer_type: "Odometer Type",
    odometer_val: "Odometer Value",
    protocol_id: "Protocol",
    business_type: "Business Type",
    vehicle_type: "Vehicle Type",
    measurement_type: "Measurement Type",
    max_capacity: "Max Capacity",
    seats: "Seats",
  },

  placeholders: {
    name: "Enter unit name",
    plate_number: "Enter plate number",
    device_type: "Select device type",
    device_serial_number: "Enter device serial number",
    sim_card_number: "Enter SIM card number",
    sim_card_serial_number: "Enter SIM card serial number",
    imei: "Enter IMEI",
    icon: "Select icon",
    password: "Enter password",
    operation_code: "Enter operation code",
    engine_hours_type: "Select engine hours type",
    engine_hours_value: "Enter engine hours value",
    odometer_type: "Select odometer type",
    odometer_val: "Enter odometer value",
    protocol_id: "Select protocol",
    business_type: "Select business type",
    vehicle_type: "Enter vehicle type",
    measurement_type: "Select measurement type",
    max_capacity: "Enter max capacity",
    seats: "Enter number of seats",
  },

  validation: {
    nameRequired: "Unit name is required",
    plateNumberRequired: "Plate number is required",
    deviceTypeRequired: "Device type is required",
    deviceSerialNumberRequired: "Device serial number is required",
    simCardNumberRequired: "SIM card number is required",
    simCardSerialNumberRequired: "SIM card serial number is required",
    imeiRequired: "IMEI is required",
    iconRequired: "Icon is required",
    passwordRequired: "Password is required",
    passwordMin: "Password must be at least 8 characters",
    operationCodeRequired: "Operation code is required",
    engineHoursTypeRequired: "Engine hours type is required",
    engineHoursValueRequired: "Engine hours value is required",
    engineHoursValuePositive: "Engine hours value must be greater than 0",
    odometerTypeRequired: "Odometer type is required",
    odometerValRequired: "Odometer value is required",
    odometerValPositive: "Odometer value must be greater than 0",
    protocolIdRequired: "Protocol ID is required",
    businessTypeRequired: "Business type is required",
    vehicleTypeRequired: "Vehicle type is required",
    measurementTypeRequired: "Measurement type is required",
    maxCapacityRequired: "Max capacity is required",
    maxCapacityPositive: "Max capacity must be greater than 0",
    seatsRequired: "Seats count is required",
    seatsPositive: "Seats count must be at least 1",
  },
};
