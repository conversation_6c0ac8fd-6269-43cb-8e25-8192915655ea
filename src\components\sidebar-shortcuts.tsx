import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { useSidebar } from "@/components/ui/sidebar";
import { ChevronDown, Command, Plus, Search, Settings } from "lucide-react";
import { useState } from "react";
import { useTranslation } from "react-i18next";

export function SidebarShortcuts() {
  const { state } = useSidebar();
  const [isOpen, setIsOpen] = useState(false);
  const { t } = useTranslation();

  const shortcuts = [
    { icon: Command, label: t("ui.sidebar.commandPalette"), keys: ["⌘", "K"] },
    { icon: Search, label: t("common.actions.search"), keys: ["⌘", "F"] },
    { icon: Plus, label: t("ui.sidebar.newItem"), keys: ["⌘", "N"] },
    { icon: Settings, label: t("ui.sidebar.settings"), keys: ["⌘", ","] },
  ];

  if (state === "collapsed") {
    return (
      <div className="flex flex-col items-center gap-1 p-2">
        <Button
          variant="ghost"
          size="sm"
          className="h-8 w-8 rounded-lg bg-gradient-to-r from-cyan-400/10 to-fuchsia-500/10 p-0 hover:from-cyan-400/20 hover:to-fuchsia-500/20"
        >
          <Command className="h-4 w-4 text-cyan-500" />
        </Button>
      </div>
    );
  }

  return (
    <Collapsible open={isOpen} onOpenChange={setIsOpen}>
      <CollapsibleTrigger asChild>
        <Button
          variant="ghost"
          className="hover:bg-accent/50 w-full justify-between rounded-xl p-3 text-left"
        >
          <div className="flex items-center gap-2">
            <Command className="h-4 w-4 text-cyan-500" />
            <span className="text-sm font-medium">
              {t("ui.sidebar.shortcuts")}
            </span>
          </div>
          <ChevronDown className="text-muted-foreground h-4 w-4 transition-transform data-[state=open]:rotate-180" />
        </Button>
      </CollapsibleTrigger>
      <CollapsibleContent className="space-y-1 px-2 pb-2">
        {shortcuts.map((shortcut, index) => (
          <div
            key={index}
            className="hover:bg-accent/30 flex items-center justify-between rounded-lg p-2"
          >
            <div className="flex items-center gap-2">
              <shortcut.icon className="text-muted-foreground h-3.5 w-3.5" />
              <span className="text-muted-foreground text-xs">
                {shortcut.label}
              </span>
            </div>
            <div className="flex gap-1">
              {shortcut.keys.map((key, keyIndex) => (
                <Badge
                  key={keyIndex}
                  variant="secondary"
                  className="h-5 px-1.5 font-mono text-xs"
                >
                  {key}
                </Badge>
              ))}
            </div>
          </div>
        ))}
      </CollapsibleContent>
    </Collapsible>
  );
}
