import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Activity,
  Clock,
  FileText,
  Heart,
  HelpCircle,
  Mail,
  MessageSquare,
} from "lucide-react";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

export default function Footer() {
  const [time, setTime] = useState(new Date());
  const { t } = useTranslation();

  useEffect(() => {
    const timer = setInterval(() => {
      setTime(new Date());
    }, 1000);

    return () => clearInterval(timer); // cleanup
  }, []);

  const quickLinks = [
    {
      label: t("ui.footer.help"),
      href: "#",
      icon: HelpCircle,
      color: "text-cyan-500 hover:text-cyan-400",
    },
    {
      label: t("ui.footer.feedback"),
      href: "#",
      icon: MessageSquare,
      color: "text-fuchsia-500 hover:text-fuchsia-400",
    },
    {
      label: t("ui.footer.documentation"),
      href: "#",
      icon: FileText,
      color: "text-emerald-500 hover:text-emerald-400",
    },
    {
      label: t("ui.footer.contact"),
      href: "#",
      icon: Mail,
      color: "text-amber-500 hover:text-amber-400",
    },
  ];

  return (
    <footer className="relative w-full overflow-hidden rounded-xl border border-white/10 bg-gradient-to-r from-slate-900/50 via-slate-800/50 to-slate-900/50 shadow-2xl backdrop-blur-xl">
      {/* Animated background gradient */}
      <div className="absolute inset-0 animate-pulse bg-gradient-to-r from-fuchsia-500/5 via-cyan-400/5 to-purple-500/5" />

      {/* Content */}
      <div className="relative px-6 py-4">
        <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
          {/* Left section - Copyright and status */}
          <div className="flex flex-col gap-3 sm:flex-row sm:items-center">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-slate-300">
                {t("ui.footer.copyright", { year: new Date().getFullYear() })}
              </span>
            </div>

            <div className="flex items-center gap-3">
              <Separator
                orientation="vertical"
                className="hidden h-4 bg-slate-600 sm:block"
              />

              {/* Live time */}
              <div className="flex items-center gap-1.5 rounded-lg bg-gradient-to-r from-cyan-500/10 to-blue-500/10 px-2.5 py-1 ring-1 ring-cyan-500/20">
                <Clock className="h-3 w-3 text-cyan-400" />
                <span className="font-mono text-xs text-cyan-300">
                  {time.toLocaleTimeString()}
                </span>
              </div>

              {/* System status */}
              <div className="flex items-center gap-1.5 rounded-lg bg-gradient-to-r from-emerald-500/10 to-green-500/10 px-2.5 py-1 ring-1 ring-emerald-500/20">
                <Activity className="h-3 w-3 text-emerald-400" />
                <span className="text-xs font-medium text-emerald-300">
                  {t("ui.footer.status")}
                </span>
              </div>
            </div>
          </div>

          {/* Right section - Quick links and version */}
          <div className="flex flex-col gap-3 sm:flex-row sm:items-center">
            {/* Quick links */}
            <div className="flex items-center gap-1">
              {quickLinks.map((link, index) => {
                const Icon = link.icon;
                return (
                  <a
                    key={index}
                    href={link.href}
                    className={`group flex items-center gap-1.5 rounded-lg px-2.5 py-1.5 text-xs font-medium transition-all duration-200 hover:bg-white/5 ${link.color}`}
                    title={link.label}
                  >
                    <Icon className="h-3.5 w-3.5 transition-transform group-hover:scale-110" />
                    <span className="hidden sm:inline">{link.label}</span>
                  </a>
                );
              })}
            </div>

            <Separator
              orientation="vertical"
              className="hidden h-4 bg-slate-600 sm:block"
            />

            {/* Version and made with love */}
            <div className="flex items-center gap-3">
              <Badge
                variant="outline"
                className="border-slate-600 bg-slate-800/50 text-slate-300"
              >
                {t("ui.footer.version", { version: t("ui.app.version") })}
              </Badge>

              <div className="flex items-center gap-1 text-xs text-slate-400">
                <span>{t("ui.footer.madeWith").split("❤️")[0]}</span>
                <Heart className="h-3 w-3 animate-pulse fill-red-500 text-red-500" />
                <span>{t("ui.footer.madeWith").split("❤️")[1]}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
