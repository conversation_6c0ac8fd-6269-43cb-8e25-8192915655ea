import { Badge } from "@/components/ui/badge";
import {
  Clock,
  FileText,
  Heart,
  HelpCircle,
  Mail,
  MessageSquare,
} from "lucide-react";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

export default function Footer() {
  const [time, setTime] = useState(new Date());
  const { t } = useTranslation();

  useEffect(() => {
    const timer = setInterval(() => {
      setTime(new Date());
    }, 1000);

    return () => clearInterval(timer); // cleanup
  }, []);

  const quickLinks = [
    {
      label: t("ui.footer.help"),
      href: "#",
      icon: HelpCircle,
      color: "text-cyan-500 hover:text-cyan-400",
    },
    {
      label: t("ui.footer.feedback"),
      href: "#",
      icon: MessageSquare,
      color: "text-fuchsia-500 hover:text-fuchsia-400",
    },
    {
      label: t("ui.footer.documentation"),
      href: "#",
      icon: FileText,
      color: "text-emerald-500 hover:text-emerald-400",
    },
    {
      label: t("ui.footer.contact"),
      href: "#",
      icon: Mail,
      color: "text-amber-500 hover:text-amber-400",
    },
  ];

  return (
    <footer className="relative w-full overflow-hidden rounded-t-xl">
      {/* Modern glass morphism background */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-900/95 via-slate-800/90 to-slate-900/95 backdrop-blur-2xl" />

      {/* Subtle animated border */}
      <div className="absolute inset-0 rounded-t-xl border-t border-white/10">
        <div className="absolute inset-0 animate-pulse bg-gradient-to-r from-transparent via-white/5 to-transparent" />
      </div>

      {/* Main content container */}
      <div className="relative">
        {/* Top section with status indicators */}
        <div className="border-b border-white/5 px-6 py-4">
          <div className="flex flex-wrap items-center justify-between gap-4">
            {/* Live indicators */}
            <div className="flex items-center gap-4">
              {/* System status */}
              <div className="flex items-center gap-2 rounded-full bg-emerald-500/10 px-3 py-1.5 ring-1 ring-emerald-500/20">
                <div className="h-2 w-2 animate-pulse rounded-full bg-emerald-400" />
                <span className="text-xs font-medium text-emerald-300">
                  {t("ui.footer.status")}
                </span>
              </div>

              {/* Live time */}
              <div className="flex items-center gap-2 rounded-full bg-cyan-500/10 px-3 py-1.5 ring-1 ring-cyan-500/20">
                <Clock className="h-3.5 w-3.5 text-cyan-400" />
                <span className="font-mono text-xs text-cyan-300">
                  {time.toLocaleTimeString()}
                </span>
              </div>
            </div>

            {/* Version badge */}
            <Badge
              variant="outline"
              className="border-white/20 bg-white/5 text-slate-300 backdrop-blur-sm"
            >
              {t("ui.footer.version", { version: t("ui.app.version") })}
            </Badge>
          </div>
        </div>

        {/* Bottom section with links and copyright */}
        <div className="px-6 py-4">
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            {/* Quick links */}
            <div className="flex flex-wrap items-center gap-2">
              {quickLinks.map((link, index) => {
                const Icon = link.icon;
                return (
                  <a
                    key={index}
                    href={link.href}
                    className={`group flex items-center gap-2 rounded-lg px-3 py-2 text-sm font-medium transition-all duration-300 hover:scale-105 hover:bg-white/10 ${link.color}`}
                    title={link.label}
                  >
                    <Icon className="h-4 w-4 transition-transform group-hover:rotate-12" />
                    <span className="hidden sm:inline">{link.label}</span>
                  </a>
                );
              })}
            </div>

            {/* Copyright and made with love */}
            <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:gap-4">
              {/* Copyright */}
              <span className="text-sm text-slate-400">
                {t("ui.footer.copyright", { year: new Date().getFullYear() })}
              </span>

              {/* Made with love */}
              <div className="flex items-center gap-1.5">
                <span className="text-sm text-slate-500">
                  {t("ui.footer.madeWith").split("❤️")[0]}
                </span>
                <div className="relative">
                  <Heart className="h-4 w-4 animate-pulse fill-red-500 text-red-500" />
                  <div className="absolute inset-0 h-4 w-4 animate-ping fill-red-500/20 text-red-500/20" />
                </div>
                <span className="text-sm text-slate-500">
                  {t("ui.footer.madeWith").split("❤️")[1]}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Decorative bottom gradient */}
        <div className="absolute right-0 bottom-0 left-0 h-px bg-gradient-to-r from-transparent via-white/20 to-transparent" />
      </div>
    </footer>
  );
}
