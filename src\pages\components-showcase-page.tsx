import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { ColorInput } from "@/components/ui/color-input";
import {
  Combobox,
  MultiCombobox,
  type ComboboxOption,
} from "@/components/ui/combobox";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Textarea } from "@/components/ui/textarea";
import { SearchIcon } from "lucide-react";
import { useState } from "react";

export default function ComponentsShowcasePage() {
  const [inputValue, setInputValue] = useState("");
  const [textareaValue, setTextareaValue] = useState("");
  const [selectValue, setSelectValue] = useState("");
  const [colorValue, setColorValue] = useState("#ff6b6b");
  const [switchValue, setSwitchValue] = useState(false);
  const [checkboxValue, setCheckboxValue] = useState(false);
  const [comboboxValue, setComboboxValue] = useState("");
  const [multiComboboxValues, setMultiComboboxValues] = useState<string[]>([]);
  const [commandOpen, setCommandOpen] = useState(false);

  // Sample options for combobox
  const comboboxOptions: ComboboxOption[] = [
    { value: "react", label: "React" },
    { value: "vue", label: "Vue.js" },
    { value: "angular", label: "Angular" },
    { value: "svelte", label: "Svelte" },
    { value: "nextjs", label: "Next.js" },
    { value: "nuxtjs", label: "Nuxt.js" },
    { value: "gatsby", label: "Gatsby" },
    { value: "remix", label: "Remix" },
    { value: "astro", label: "Astro" },
    { value: "solid", label: "SolidJS" },
  ];

  const skillOptions: ComboboxOption[] = [
    { value: "javascript", label: "JavaScript" },
    { value: "typescript", label: "TypeScript" },
    { value: "python", label: "Python" },
    { value: "java", label: "Java" },
    { value: "csharp", label: "C#" },
    { value: "php", label: "PHP" },
    { value: "ruby", label: "Ruby" },
    { value: "go", label: "Go" },
    { value: "rust", label: "Rust" },
    { value: "swift", label: "Swift" },
  ];

  const sampleData = [
    {
      id: 1,
      name: "John Doe",
      email: "<EMAIL>",
      role: "Admin",
      status: "Active",
    },
    {
      id: 2,
      name: "Jane Smith",
      email: "<EMAIL>",
      role: "User",
      status: "Active",
    },
    {
      id: 3,
      name: "Bob Johnson",
      email: "<EMAIL>",
      role: "Editor",
      status: "Inactive",
    },
    {
      id: 4,
      name: "Alice Brown",
      email: "<EMAIL>",
      role: "User",
      status: "Active",
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-fuchsia-500/10 to-cyan-400/10 p-8">
      <div className="mx-auto max-w-7xl space-y-8">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-gradient mb-4 text-4xl font-extrabold">
            Modern UI Components Showcase
          </h1>
          <p className="text-muted-foreground text-lg">
            Experience the new modern design system with enhanced gradients and
            interactions
          </p>
        </div>

        {/* Buttons Section */}
        <Card className="card-shadow-modern">
          <CardHeader>
            <CardTitle className="text-gradient text-2xl">Buttons</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
              <div className="space-y-3">
                <Label className="text-sm font-semibold">
                  Default Variants
                </Label>
                <div className="space-y-2">
                  <Button className="btn-press w-full">Default Button</Button>
                  <Button variant="outline" className="btn-press w-full">
                    Outline Button
                  </Button>
                  <Button variant="secondary" className="btn-press w-full">
                    Secondary Button
                  </Button>
                  <Button variant="ghost" className="btn-press w-full">
                    Ghost Button
                  </Button>
                </div>
              </div>

              <div className="space-y-3">
                <Label className="text-sm font-semibold">
                  Gradient Variants
                </Label>
                <div className="space-y-2">
                  <Button variant="gradient" className="btn-press w-full">
                    Gradient Button
                  </Button>
                  <Button
                    variant="gradient-outline"
                    className="btn-press w-full"
                  >
                    Gradient Outline
                  </Button>
                  <Button variant="destructive" className="btn-press w-full">
                    Destructive Button
                  </Button>
                  <Button variant="link" className="btn-press w-full">
                    Link Button
                  </Button>
                </div>
              </div>

              <div className="space-y-3">
                <Label className="text-sm font-semibold">Sizes</Label>
                <div className="space-y-2">
                  <Button size="sm" className="btn-press w-full">
                    Small Button
                  </Button>
                  <Button size="default" className="btn-press w-full">
                    Default Size
                  </Button>
                  <Button size="lg" className="btn-press w-full">
                    Large Button
                  </Button>
                  <Button size="icon" className="btn-press">
                    🚀
                  </Button>
                </div>
              </div>

              <div className="space-y-3">
                <Label className="text-sm font-semibold">States</Label>
                <div className="space-y-2">
                  <Button className="btn-press w-full">Normal State</Button>
                  <Button disabled className="w-full">
                    Disabled State
                  </Button>
                  <Button className="btn-press w-full" aria-invalid>
                    Invalid State
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Combobox & Command Section */}
        <Card className="card-shadow-modern">
          <CardHeader>
            <CardTitle className="text-gradient text-2xl">
              Combobox & Command Components
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>Single Select Combobox</Label>
                  <Combobox
                    options={comboboxOptions}
                    value={comboboxValue}
                    onValueChange={setComboboxValue}
                    placeholder="Choose a framework..."
                    searchPlaceholder="Search frameworks..."
                    clearable
                  />
                </div>

                <div className="space-y-2">
                  <Label>Multi Select Combobox</Label>
                  <MultiCombobox
                    options={skillOptions}
                    values={multiComboboxValues}
                    onValuesChange={setMultiComboboxValues}
                    placeholder="Select your skills..."
                    searchPlaceholder="Search skills..."
                    maxSelected={5}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Small Size Combobox</Label>
                  <Combobox
                    options={comboboxOptions.slice(0, 5)}
                    placeholder="Small combobox..."
                    size="sm"
                  />
                </div>
              </div>

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>Command Palette Demo</Label>
                  <Popover open={commandOpen} onOpenChange={setCommandOpen}>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className="text-muted-foreground w-full justify-start"
                      >
                        <SearchIcon className="mr-2 h-4 w-4" />
                        Search commands...
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-80 p-0" align="start">
                      <Command>
                        <CommandInput placeholder="Type a command or search..." />
                        <CommandList>
                          <CommandEmpty>No results found.</CommandEmpty>
                          <CommandGroup heading="Suggestions">
                            <CommandItem>
                              <SearchIcon className="mr-2 h-4 w-4" />
                              <span>Search Files</span>
                            </CommandItem>
                            <CommandItem>
                              <span>Open Recent</span>
                            </CommandItem>
                            <CommandItem>
                              <span>New File</span>
                            </CommandItem>
                          </CommandGroup>
                          <CommandGroup heading="Settings">
                            <CommandItem>
                              <span>Profile</span>
                            </CommandItem>
                            <CommandItem>
                              <span>Preferences</span>
                            </CommandItem>
                            <CommandItem>
                              <span>Keyboard Shortcuts</span>
                            </CommandItem>
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                </div>

                <div className="space-y-2">
                  <Label>Large Size Combobox</Label>
                  <Combobox
                    options={comboboxOptions.slice(0, 4)}
                    placeholder="Large combobox..."
                    size="lg"
                  />
                </div>

                <div className="space-y-2">
                  <Label>Disabled Combobox</Label>
                  <Combobox
                    options={comboboxOptions}
                    placeholder="Disabled combobox..."
                    disabled
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Form Inputs Section */}
        <Card className="card-shadow-modern">
          <CardHeader>
            <CardTitle className="text-gradient text-2xl">
              Form Inputs
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="text-input">Text Input</Label>
                  <Input
                    id="text-input"
                    placeholder="Enter your text here..."
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                    className="input-focus-animation"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email-input">Email Input</Label>
                  <Input
                    id="email-input"
                    type="email"
                    placeholder="<EMAIL>"
                    className="input-focus-animation"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="password-input">Password Input</Label>
                  <Input
                    id="password-input"
                    type="password"
                    placeholder="Enter your password"
                    className="input-focus-animation"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="select-input">Select Input</Label>
                  <Select value={selectValue} onValueChange={setSelectValue}>
                    <SelectTrigger>
                      <SelectValue placeholder="Choose an option" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="option1">Option 1</SelectItem>
                      <SelectItem value="option2">Option 2</SelectItem>
                      <SelectItem value="option3">Option 3</SelectItem>
                      <SelectItem value="option4">Option 4</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="textarea-input">Textarea</Label>
                  <Textarea
                    id="textarea-input"
                    placeholder="Enter your message here..."
                    value={textareaValue}
                    onChange={(e) => setTextareaValue(e.target.value)}
                    className="input-focus-animation"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="color-input">Color Input</Label>
                  <ColorInput
                    value={colorValue}
                    onChange={setColorValue}
                    placeholder="Select a color"
                  />
                </div>

                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <Switch
                      checked={switchValue}
                      onCheckedChange={setSwitchValue}
                    />
                    <Label>Enable notifications</Label>
                  </div>

                  <div className="flex items-center space-x-3">
                    <Checkbox
                      checked={checkboxValue}
                      onCheckedChange={(checked) =>
                        setCheckboxValue(checked === true)
                      }
                    />
                    <Label>I agree to the terms and conditions</Label>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Table Section */}
        <Card className="card-shadow-modern">
          <CardHeader>
            <CardTitle className="text-gradient text-2xl">
              Modern Table
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>ID</TableHead>
                  <TableHead>Name</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {sampleData.map((item) => (
                  <TableRow key={item.id} className="table-row-modern">
                    <TableCell className="font-medium">{item.id}</TableCell>
                    <TableCell>{item.name}</TableCell>
                    <TableCell>{item.email}</TableCell>
                    <TableCell>
                      <span className="text-foreground inline-flex items-center rounded-full bg-gradient-to-r from-fuchsia-500/10 to-cyan-400/10 px-2.5 py-0.5 text-xs font-medium">
                        {item.role}
                      </span>
                    </TableCell>
                    <TableCell>
                      <span
                        className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                          item.status === "Active"
                            ? "bg-green-500/10 text-green-700 dark:text-green-400"
                            : "bg-red-500/10 text-red-700 dark:text-red-400"
                        }`}
                      >
                        {item.status}
                      </span>
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button
                          size="sm"
                          variant="outline"
                          className="btn-press"
                        >
                          Edit
                        </Button>
                        <Button
                          size="sm"
                          variant="destructive"
                          className="btn-press"
                        >
                          Delete
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Interactive Demo Section */}
        <Card className="card-shadow-modern">
          <CardHeader>
            <CardTitle className="text-gradient text-2xl">
              Interactive Demo
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Form Preview</h3>
                <div className="from-background/50 to-background/30 border-border/50 space-y-4 rounded-lg border bg-gradient-to-br p-6">
                  <Input
                    placeholder="Your name"
                    className="input-focus-animation"
                  />
                  <Input
                    type="email"
                    placeholder="<EMAIL>"
                    className="input-focus-animation"
                  />
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select your role" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="admin">Administrator</SelectItem>
                      <SelectItem value="user">User</SelectItem>
                      <SelectItem value="editor">Editor</SelectItem>
                    </SelectContent>
                  </Select>
                  <Combobox
                    options={comboboxOptions.slice(0, 6)}
                    placeholder="Choose your framework..."
                    searchPlaceholder="Search frameworks..."
                  />
                  <MultiCombobox
                    options={skillOptions.slice(0, 6)}
                    placeholder="Select your skills..."
                    searchPlaceholder="Search skills..."
                    maxSelected={3}
                  />
                  <Textarea
                    placeholder="Tell us about yourself..."
                    className="input-focus-animation"
                  />
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      onCheckedChange={(checked) =>
                        console.log("Newsletter subscription:", checked)
                      }
                    />
                    <Label className="text-sm">Subscribe to newsletter</Label>
                  </div>
                  <Button className="btn-press w-full">Submit Form</Button>
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Component States</h3>
                <div className="from-background/50 to-background/30 border-border/50 space-y-4 rounded-lg border bg-gradient-to-br p-6">
                  <div className="space-y-2">
                    <Label>Normal Input</Label>
                    <Input
                      placeholder="Normal state"
                      className="input-focus-animation"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Focused Input (click to see)</Label>
                    <Input
                      placeholder="Focus me!"
                      className="input-focus-animation"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Invalid Input</Label>
                    <Input
                      placeholder="Invalid state"
                      aria-invalid
                      className="input-focus-animation"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Disabled Input</Label>
                    <Input placeholder="Disabled state" disabled />
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="py-8 text-center">
          <p className="text-muted-foreground">
            Modern UI components with enhanced gradients, animations, and
            accessibility features
          </p>
        </div>
      </div>
    </div>
  );
}
