import { Plus } from "lucide-react";
import { useTranslation } from "react-i18next";
import { <PERSON> } from "react-router";
import { Button } from "../ui/button";

type PageMainHeaderProps = {
  title: string;
  addUrl?: string;
  addText?: string;
  onAddClick?: () => void;
};

export default function PageMainHeader({
  title,
  addUrl,
  addText,
  onAddClick,
}: PageMainHeaderProps) {
  const { t } = useTranslation();

  const renderButton = () => {
    if (!addText) return null;

    if (addUrl) {
      return (
        <Link to={addUrl}>
          <Button className="items-center gap-2 rounded-xl bg-gradient-to-r from-cyan-400 to-blue-500 px-6 py-2.5 text-sm font-medium text-white shadow-lg backdrop-blur-sm transition-all duration-300 hover:scale-105 hover:from-cyan-500 hover:to-blue-600 hover:shadow-cyan-400/20">
            <Plus className="h-4 w-4" />
            {t(addText)}
          </Button>
        </Link>
      );
    }

    if (onAddClick) {
      return (
        <Button
          onClick={onAddClick}
          className="items-center gap-2 rounded-xl bg-gradient-to-r from-cyan-400 to-blue-500 px-6 py-2.5 text-sm font-medium text-white shadow-lg backdrop-blur-sm transition-all duration-300 hover:scale-105 hover:from-cyan-500 hover:to-blue-600 hover:shadow-cyan-400/20"
        >
          <Plus className="h-4 w-4" />
          {t(addText)}
        </Button>
      );
    }

    return null;
  };

  return (
    <div className="relative flex items-center justify-between overflow-hidden rounded-2xl border border-white/10 bg-gradient-to-br from-slate-900/95 via-slate-800/90 to-slate-900/95 px-8 py-6 shadow-2xl backdrop-blur-2xl">
      {/* Decorative background elements */}
      <div className="absolute -top-16 -right-16 h-32 w-32 animate-pulse rounded-full bg-gradient-to-br from-cyan-400/10 to-blue-500/10 opacity-60 blur-3xl" />
      <div className="absolute -bottom-12 -left-12 h-24 w-24 animate-pulse rounded-full bg-gradient-to-br from-purple-400/8 to-pink-500/8 opacity-40 blur-2xl" />

      {/* Animated border */}
      <div className="absolute inset-0 animate-pulse rounded-2xl bg-gradient-to-r from-cyan-400/20 via-transparent to-blue-500/20 p-[1px]">
        <div className="h-full w-full rounded-2xl bg-gradient-to-br from-slate-900/95 via-slate-800/90 to-slate-900/95 backdrop-blur-2xl" />
      </div>

      {/* Content */}
      <div className="relative z-10 flex items-center gap-6">
        <div className="h-14 w-1 rounded-full bg-gradient-to-b from-cyan-400 to-blue-500 shadow-lg shadow-cyan-400/20" />
        <div>
          <h1 className="bg-gradient-to-br from-slate-100 to-slate-300 bg-clip-text text-3xl leading-tight font-extrabold text-transparent drop-shadow-lg">
            {t(title)}
          </h1>
          <div className="mt-3 h-1 w-20 rounded-full bg-gradient-to-r from-cyan-400 to-blue-500 shadow-sm shadow-cyan-400/20" />
        </div>
      </div>

      {/* Action Button */}
      {renderButton() && (
        <div className="relative z-10 flex items-center gap-3">
          {renderButton()}
        </div>
      )}
    </div>
  );
}
