import { Plus } from "lucide-react";
import { useTranslation } from "react-i18next";
import { Link } from "react-router";
import { Button } from "../ui/button";

type PageMainHeaderProps = {
  title: string;
  addUrl?: string;
  addText?: string;
  onAddClick?: () => void;
};

export default function PageMainHeader({
  title,
  addUrl,
  addText,
  onAddClick,
}: PageMainHeaderProps) {
  const { t } = useTranslation();

  const renderButton = () => {
    if (!addText) return null;

    if (addUrl) {
      return (
        <Link to={addUrl}>
          <Button className="bg-brand-secondary/10 text-brand-secondary items-center gap-2 rounded-lg px-4 py-2 text-sm font-medium backdrop-blur-sm">
            <Plus className="mr-2 h-4 w-4" />
            {t(addText)}
          </Button>
        </Link>
      );
    }

    if (onAddClick) {
      return (
        <Button
          onClick={onAddClick}
          className="bg-brand-secondary/10 text-brand-secondary items-center gap-2 rounded-lg px-4 py-2 text-sm font-medium backdrop-blur-sm"
        >
          <Plus className="mr-2 h-4 w-4" />
          {t(addText)}
        </Button>
      );
    }

    return null;
  };

  return (
    <div className="border-brand-primary/20 from-brand-primary/10 to-brand-secondary/10 relative flex items-center justify-between overflow-hidden rounded-2xl border bg-gradient-to-br px-6 py-4 shadow-xl backdrop-blur-sm">
      {/* Decorative background elements */}
      <div className="bg-gradient-radial from-brand-primary/30 absolute top-0 right-0 h-32 w-32 translate-x-16 -translate-y-16 rounded-full to-transparent opacity-30 blur-3xl" />
      <div className="bg-gradient-radial from-brand-secondary/20 absolute bottom-0 left-0 h-24 w-24 -translate-x-12 translate-y-12 rounded-full to-transparent opacity-20 blur-2xl" />

      {/* Content */}
      <div className="relative z-10 flex items-center gap-4">
        <div className="from-brand-primary to-brand-secondary h-12 w-2 rounded-full bg-gradient-to-b" />
        <div>
          <h1 className="from-brand-primary to-brand-secondary bg-gradient-to-br bg-clip-text text-3xl leading-tight font-extrabold text-transparent drop-shadow-lg">
            {t(title)}
          </h1>
          <div className="from-brand-primary to-brand-secondary mt-2 h-1 w-16 rounded-full bg-gradient-to-r" />
        </div>
      </div>

      {/* Action Button */}
      {renderButton() && (
        <div className="relative z-10 flex items-center gap-3">
          {renderButton()}
        </div>
      )}
    </div>
  );
}
