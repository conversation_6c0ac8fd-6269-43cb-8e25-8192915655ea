import { Plus } from "lucide-react";
import { useTranslation } from "react-i18next";
import { Link } from "react-router";
import { Button } from "../ui/button";

type PageMainHeaderProps = {
  title: string;
  addUrl?: string;
  addText?: string;
  onAddClick?: () => void;
};

export default function PageMainHeader({
  title,
  addUrl,
  addText,
  onAddClick,
}: PageMainHeaderProps) {
  const { t } = useTranslation();

  const renderButton = () => {
    if (!addText) return null;

    const buttonClasses =
      "inline-flex items-center gap-2 rounded-lg bg-[var(--brand-secondary)] px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-[var(--brand-primary)] focus:outline-none focus:ring-2 focus:ring-[var(--brand-secondary)]/30";

    if (addUrl) {
      return (
        <Link to={addUrl}>
          <Button className={buttonClasses}>
            <Plus className="h-4 w-4" />
            {t(addText)}
          </Button>
        </Link>
      );
    }

    if (onAddClick) {
      return (
        <Button onClick={onAddClick} className={buttonClasses}>
          <Plus className="h-4 w-4" />
          {t(addText)}
        </Button>
      );
    }

    return null;
  };

  return (
    <div className="flex items-center justify-between rounded-lg border border-white/20 bg-white/5 px-6 py-4 backdrop-blur-sm">
      {/* Content */}
      <div className="flex items-center gap-4">
        <div className="h-10 w-1 rounded-full bg-[var(--brand-secondary)]" />
        <div>
          <h1 className="text-2xl font-bold text-slate-100">{t(title)}</h1>
          <div className="mt-1 h-0.5 w-16 rounded-full bg-[var(--brand-secondary)]/60" />
        </div>
      </div>

      {/* Action Button */}
      {renderButton()}
    </div>
  );
}
