import { DataTableColumnHeader } from "@/components/table/data-table-column-header";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import type { Column, ColumnDef, Row } from "@tanstack/react-table";
import type { TFunction } from "i18next";
import type { ReactNode } from "react";

type ActionConfig<T> = {
  icon: ReactNode;
  label: string;
  onClick?: (row: T) => void;
  asChild?: boolean;
};

type ColumnConfig<T> = {
  key: keyof T | string; // ✅ يسمح بمسارات nested
  title: string;
  cell?: (row: T) => ReactNode;
};

interface CreateColumnsOptions<T extends object> {
  t: TFunction;
  entityKey: string;
  fields: ColumnConfig<T>[];
  actions?: ActionConfig<T>[];
}

// ✅ helper بدون any
function getNestedValue<T extends object>(obj: T, path: string): unknown {
  return path.split(".").reduce<unknown>((acc, part) => {
    if (acc && typeof acc === "object" && part in acc) {
      return (acc as Record<string, unknown>)[part];
    }
    return undefined;
  }, obj);
}

export function createColumns<T extends object>({
  t,
  entityKey,
  fields,
  actions = [],
}: CreateColumnsOptions<T>): ColumnDef<T>[] {
  const columns: ColumnDef<T>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label={t(`${entityKey}.table.selectAll`)}
        />
      ),
      cell: ({ row }: { row: Row<T> }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label={t(`${entityKey}.table.selectRow`)}
        />
      ),
    },
    ...fields.map((f) => ({
      id: f.key as string,
      accessorFn: (row: T) => getNestedValue(row, f.key as string),
      header: ({ column }: { column: Column<T, unknown> }) => (
        <DataTableColumnHeader column={column} title={t(f.title)} />
      ),
      cell: ({ row }: { row: Row<T> }) =>
        f.cell ? (
          f.cell(row.original)
        ) : (
          <div>
            {String(getNestedValue(row.original, f.key as string) ?? "")}
          </div>
        ),
    })),
  ];

  if (actions.length > 0) {
    columns.push({
      id: "actions",
      header: () => (
        <div className="text-start">{t(`${entityKey}.actions.title`)}</div>
      ),
      cell: ({ row }: { row: Row<T> }) => (
        <div className="flex gap-2">
          {actions.map((action, idx) => (
            <Tooltip key={idx}>
              <TooltipTrigger asChild={!!action.asChild}>
                <Button
                  variant="secondary"
                  size="icon"
                  className="size-7"
                  onClick={() => action.onClick?.(row.original)}
                >
                  {action.icon}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>{action.label}</p>
              </TooltipContent>
            </Tooltip>
          ))}
        </div>
      ),
    });
  }

  return columns;
}
