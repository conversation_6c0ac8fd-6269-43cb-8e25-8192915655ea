import {
  openConfirmDeleteUser<PERSON>tom,
  openUpdateUser<PERSON>tom,
} from "@/atoms/app/open-atoms";
import { selectedUser<PERSON>tom } from "@/atoms/app/selected-atoms";
import type { User } from "@/types/users";
import type { TFunction } from "i18next";
import { Edit, Trash2 } from "lucide-react";
import { createColumns } from "../utils/columns-factory";

export const UserColumns = (t: TFunction) =>
  createColumns<User>({
    t,
    entityKey: "users",
    fields: [
      { key: "name", title: "common.table.name" },
      { key: "email", title: "users.table.email" },
      { key: "phone", title: "users.table.phone" },
      {
        key: "is_active",
        title: "users.table.isActive",
        cell: (user) =>
          user.is_active ? t("common.boolean.true") : t("common.boolean.false"),
      },
    ],
    actions: [
      {
        icon: <Edit />,
        label: t("common.actions.edit"),
        onClick: (row) => {
          selectedUserAtom.change("slug", row.slug);
          openUpdateUserAtom.open();
        },
      },
      {
        icon: <Trash2 color="red" />,
        label: t("common.actions.delete"),
        onClick: (row) => {
          selectedUserAtom.change("slug", row.slug);
          openConfirmDeleteUserAtom.open();
        },
      },
    ],
  });
