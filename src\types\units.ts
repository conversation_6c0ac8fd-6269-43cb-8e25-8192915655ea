export interface GetAllUnitsResponse {
  status_code: number;
  status: string;
  message: null;
  data: Unit[];
}

export interface GetOneUnitResponse {
  status_code: number;
  status: string;
  message: null;
  data: Unit;
}
export interface UpdateUnitResponse {
  status_code: number;
  status: string;
  message: string;
  data: Unit;
}

export interface DeleteUnitResponse {
  status_code: number;
  status: string;
  message: null;
  data: string;
}

export interface CreateUnitResponse {
  status_code: number;
  status: number;
  message: string;
  data: {
    name: string;
    plate_number: string;
    device_type: number;
    device_serial_number: string;
    sim_card_number: string;
    sim_card_serial_number: string;
    imei: string;
    icon: string;
    odometer_type: number;
    odometer_val: number;
    engine_hours_type: number;
    engine_hours_value: number;
    operation_code: string;
    protocol_id: number;
    password: string;
    owner_id: number;
    creator_id: number;
    updated_at: Date;
    created_at: Date;
    id: number;
  };
}

export interface CreateUnitFormData {
  name: string;
  plate_number: string;
  device_type: number;
  device_serial_number: string;
  sim_card_number: string;
  sim_card_serial_number: string;
  imei: string;
  icon?: string;
  password: string;
  operation_code: string;
  engine_hours_type: number;
  engine_hours_value: number;
  odometer_type: number;
  odometer_val: number;
  protocol_id: number;
  business_type: number;
  vehicle_type: string;
  measurement_type: number;
  max_capacity: number;
  seats: number;
}

export interface UpdateUnitFormData {
  name: string;
  plate_number: string;
  device_type: number;
  device_serial_number: string;
  sim_card_number: string;
  sim_card_serial_number: string;
  imei: string;
  icon?: string;
  password?: string;
  operation_code: string;
  engine_hours_type: number;
  engine_hours_value: number;
  odometer_type: number;
  odometer_val: number;
  protocol_id: number;
  business_type: number;
  vehicle_type: string;
  measurement_type: number;
  max_capacity: number;
  seats: number;
}

export interface Unit {
  name: string;
  plate_number: string;
  device_serial_number: string;
  sim_card_number: string;
  imei: string;
  icon: string;
  odometer_type: {
    id: number;
    name: string;
    title: string;
  };
  protocol: {
    id: number;
    name: string;
  };
  odometer_val: number;
  engine_hours_type: {
    id: number;
    name: string;
    title: string;
  };
  engine_hours_value: number;
  operation_code: string;
  details: {
    vin: string;
    model: string;
    year: number;
    seats: number;
  };
  driver?: {
    id: number;
    name: string;
  };
}
