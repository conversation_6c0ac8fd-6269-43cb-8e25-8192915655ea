import type {
  CreateUnitFormData,
  CreateUnitResponse,
  DeleteUnitResponse,
  GetAllUnitsResponse,
  GetOneUnitResponse,
  Unit,
  UpdateUnitFormData,
  UpdateUnitResponse,
} from "@/types/units";
import { endpoint } from "@/utils/endpoints";
import { atom } from "@mongez/react-atom";
import { AxiosError } from "axios";
import toast from "react-hot-toast";

interface UnitsAtom {
  units: Unit[];
  oneUnit: Unit | null;
}

interface UnitsAtomAction {
  getUnits: () => void;
  createUnit: (formData: CreateUnitFormData, onSuccess?: () => void) => void;
  getOneUnit: (plate_number: string) => void;
  editUnit: (
    plate_number: string,
    formData: UpdateUnitFormData,
    onSuccess?: () => void,
  ) => void;
  deleteUnit: (plate_number: string, onSuccess?: () => void) => void;
}

export const unitsAtom = atom<UnitsAtom, UnitsAtomAction>({
  key: "units-atom",
  default: {
    units: [],
    oneUnit: null,
  },

  actions: {
    async getUnits() {
      try {
        const { data } = await endpoint.get<GetAllUnitsResponse>("units");
        unitsAtom.change("units", data.data);
      } catch (error) {
        if (error instanceof AxiosError) {
          toast.error(error.response?.data.message);
        } else {
          toast.error((error as Error).message);
        }
      }
    },

    async getOneUnit(plate_number: string) {
      try {
        const { data } = await endpoint.get<GetOneUnitResponse>(
          `units/${plate_number}`,
        );
        unitsAtom.change("oneUnit", data.data);
      } catch (error) {
        if (error instanceof AxiosError) {
          toast.error(error.response?.data.message);
        } else {
          toast.error((error as Error).message);
        }
      }
    },

    async createUnit(formData: CreateUnitFormData, onSuccess?: () => void) {
      try {
        const { data } = await endpoint.post<CreateUnitResponse>(
          "units",
          formData,
        );
        toast.success(data.message);
        onSuccess?.();
        unitsAtom.getUnits();
      } catch (error) {
        if (error instanceof AxiosError) {
          toast.error(error.response?.data.message);
        } else {
          toast.error((error as Error).message);
        }
      }
    },

    async editUnit(
      plate_number: string,
      formData: UpdateUnitFormData,
      onSuccess?: () => void,
    ) {
      try {
        const { data } = await endpoint.put<UpdateUnitResponse>(
          `units/${plate_number}`,
          formData,
        );
        toast.success(data.message);
        onSuccess?.();
        unitsAtom.getUnits();
      } catch (error) {
        if (error instanceof AxiosError) {
          toast.error(error.response?.data.message);
        } else {
          toast.error((error as Error).message);
        }
      }
    },

    async deleteUnit(plate_number: string, onSuccess?: () => void) {
      try {
        const { data } = await endpoint.delete<DeleteUnitResponse>(
          `units/${plate_number}`,
        );
        toast.success(data.data);
        onSuccess?.();
        unitsAtom.getUnits();
      } catch (error) {
        if (error instanceof AxiosError) {
          toast.error(error.response?.data.message);
        } else {
          toast.error((error as Error).message);
        }
      }
    },
  },
});
