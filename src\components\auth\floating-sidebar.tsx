import { SimpleTooltip } from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";
import {
  Facebook,
  Github,
  Instagram,
  Linkedin,
  Mail,
  Twitter,
} from "lucide-react";
import { Link } from "react-router";

export function FloatingSidebar() {
  const socials = [
    {
      name: "GitHub",
      icon: Github,
      href: "#",
      gradient: "from-gray-700 to-gray-900",
      hoverGradient: "hover:from-gray-600 hover:to-gray-800",
    },
    {
      name: "LinkedIn",
      icon: Linkedin,
      href: "#",
      gradient: "from-blue-600 to-blue-800",
      hoverGradient: "hover:from-blue-500 hover:to-blue-700",
    },
    {
      name: "Twitter",
      icon: Twitter,
      href: "#",
      gradient: "from-cyan-400 to-cyan-600",
      hoverGradient: "hover:from-cyan-300 hover:to-cyan-500",
    },
    {
      name: "Facebook",
      icon: Facebook,
      href: "#",
      gradient: "from-blue-500 to-blue-700",
      hoverGradient: "hover:from-blue-400 hover:to-blue-600",
    },
    {
      name: "Instagram",
      icon: Instagram,
      href: "#",
      gradient: "from-fuchsia-500 to-pink-600",
      hoverGradient: "hover:from-fuchsia-400 hover:to-pink-500",
    },
    {
      name: "Contact",
      icon: Mail,
      href: "#",
      gradient: "from-emerald-500 to-emerald-700",
      hoverGradient: "hover:from-emerald-400 hover:to-emerald-600",
    },
  ];

  return (
    <aside className="fixed top-1/2 right-6 z-50 -translate-y-1/2">
      {/* Main container with glassmorphism */}
      <div className="relative">
        {/* Background blur effect */}
        <div className="absolute inset-0 rounded-3xl border border-white/20 bg-gradient-to-b from-white/10 to-white/5 shadow-2xl backdrop-blur-xl" />

        {/* Content */}
        <div className="relative flex flex-col gap-2 p-3">
          {/* Brand indicator */}
          <div className="mb-2 flex justify-center">
            <div className="h-1 w-8 rounded-full bg-gradient-to-r from-fuchsia-500 to-cyan-400" />
          </div>

          {socials.map(
            ({ name, icon: Icon, href, gradient, hoverGradient }, index) => (
              <SimpleTooltip key={name} content={name} side="left">
                <Link
                  to={href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="group relative block"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <div
                    className={cn(
                      "relative flex h-12 w-12 items-center justify-center rounded-2xl",
                      "bg-gradient-to-br transition-all duration-300",
                      "transform hover:scale-110 hover:rotate-3",
                      "shadow-lg hover:shadow-xl",
                      gradient,
                      hoverGradient,
                    )}
                  >
                    {/* Glow effect */}
                    <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-white/20 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100" />

                    {/* Icon */}
                    <Icon className="relative z-10 h-5 w-5 text-white transition-transform duration-300 group-hover:scale-110" />

                    {/* Ripple effect */}
                    <div className="absolute inset-0 scale-0 rounded-2xl bg-white/20 transition-transform duration-300 group-active:scale-100" />
                  </div>
                </Link>
              </SimpleTooltip>
            ),
          )}

          {/* Bottom indicator */}
          <div className="mt-2 flex justify-center">
            <div className="h-1 w-8 rounded-full bg-gradient-to-r from-cyan-400 to-fuchsia-500" />
          </div>
        </div>

        {/* Floating particles effect */}
        <div className="absolute -top-2 -right-2 h-2 w-2 animate-ping rounded-full bg-fuchsia-400" />
        <div className="absolute -bottom-2 -left-2 h-1.5 w-1.5 animate-pulse rounded-full bg-cyan-400" />
      </div>
    </aside>
  );
}
