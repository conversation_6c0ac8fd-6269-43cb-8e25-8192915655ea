import { openCreate<PERSON>ser<PERSON>tom } from "@/atoms/app/open-atoms";
import { users<PERSON>tom } from "@/atoms/entities/users-atom";
import type { CreateUserFormData } from "@/types/users";
import { zodResolver } from "@hookform/resolvers/zod";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { z } from "zod";

export default function useCreateUserDialog() {
  const { t } = useTranslation();

  const FormSchema = z
    .object({
      name: z.string().nonempty(t("users.validation.nameRequired")),
      email: z.string().email(t("common.validation.email")),
      password: z.string().min(8, t("common.validation.password")),
      password_confirmation: z
        .string()
        .min(8, t("users.validation.passwordConfirmMin")),
      phone_code: z.string().nonempty(t("users.validation.phoneCodeRequired")),
      phone: z.string().nonempty(t("common.validation.phone")),
      lang: z.string().nonempty(t("users.validation.langRequired")),
    })
    .refine((data) => data.password === data.password_confirmation, {
      message: t("users.validation.passwordsMismatch"),
      path: ["password_confirmation"],
    });

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      name: "",
      email: "",
      password: "",
      password_confirmation: "",
      phone_code: "966",
      phone: "",
      lang: "en",
    },
  });

  const [isLoading, setIsLoading] = useState(false);
  const isOpened = openCreateUserAtom.useOpened();

  const closeDialog = () => {
    openCreateUserAtom.close();
    form.reset();
  };

  async function onSubmit(data: z.infer<typeof FormSchema>) {
    const formData: CreateUserFormData = {
      name: data.name,
      email: data.email,
      password: data.password,
      password_confirmation: data.password_confirmation,
      phone_code: data.phone_code,
      phone: data.phone,
      lang: data.lang as "en" | "ar",
    };

    setIsLoading(true);

    await usersAtom.createUser(formData, () => {
      closeDialog();
    });

    setIsLoading(false);
  }

  return {
    form,
    isLoading,
    isOpened,
    closeDialog,
    onSubmit,
    t,
  };
}
