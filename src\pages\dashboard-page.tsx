import { authAtom } from "@/atoms/app/auth-atom";
import { dashboardAtom } from "@/atoms/app/dashboard-atom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { OverlayLoader } from "@/components/utils/overlay-loader";
import { URLS } from "@/utils/urls";
import {
  AreaChart,
  GalleryVerticalEnd,
  MapPin,
  University,
  Users,
} from "lucide-react";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Link } from "react-router";

export default function DashboardPage() {
  const { t } = useTranslation();
  const { user } = authAtom.useValue();

  useEffect(() => {
    dashboardAtom.getStats();
  }, []);

  const { loading, stats } = dashboardAtom.useValue();

  const modules = [
    {
      title: t("dashboard.quickActions.areas.title"),
      description: t("dashboard.quickActions.areas.description"),
      icon: <PERSON><PERSON><PERSON>,
      url: URLS.areas,
      color: "var(--info)",
      value: stats?.areas,
    },
    // {
    //   title: t("dashboard.quickActions.trailers.title"),
    //   description: t("dashboard.quickActions.trailers.description"),
    //   icon: Train,
    //   url: URLS.trailers,
    //   color: "var(--success)",
    //   value: stats?.trailers,
    // },
    {
      title: t("dashboard.quickActions.drivers.title"),
      description: t("dashboard.quickActions.drivers.description"),
      icon: GalleryVerticalEnd,
      url: URLS.drivers,
      color: "var(--warning)",
      value: stats?.drivers,
    },
    {
      title: t("dashboard.quickActions.users.title"),
      description: t("dashboard.quickActions.users.description"),
      icon: Users,
      url: URLS.users,
      color: "bg-purple-500",
      value: stats?.users,
    },
    {
      title: t("dashboard.quickActions.units.title"),
      description: t("dashboard.quickActions.units.description"),
      icon: University,
      url: URLS.units,
      color: "bg-pink-500",
      value: stats?.units,
    },
  ];

  if (loading) {
    return <OverlayLoader />;
  }

  return (
    <div className="via-background/80 flex min-h-screen flex-col gap-8 bg-gradient-to-br from-fuchsia-500/10 to-cyan-400/10 p-8">
      {/* Welcome Header */}
      <div className="mb-2 flex flex-col gap-2">
        <h1 className="bg-gradient-to-r from-fuchsia-500 to-cyan-400 bg-clip-text text-4xl font-extrabold text-transparent drop-shadow-lg">
          {t("dashboard.welcome", { name: user?.name || "User" })}
        </h1>
        <p className="text-muted-foreground text-lg font-medium">
          {t("dashboard.overview")}
        </p>
      </div>

      {/* Unified Module Cards */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
        {modules.map((mod, idx) => (
          <Link key={mod.title} to={mod.url}>
            <Card
              className="animate-fade-in h-full cursor-pointer rounded-2xl border-0 shadow-md transition-all hover:scale-105 hover:shadow-2xl"
              style={{
                animationDelay: `${idx * 0.1}s`,
                background: `linear-gradient(135deg,
                  oklch(from var(--brand-secondary) l c h / 0.1),
                  oklch(from var(--brand-primary) l c h / 0.1))`,
              }}
            >
              <CardHeader className="flex flex-row items-center space-y-0 pb-2">
                <div
                  className="mr-3 flex animate-bounce items-center justify-center rounded-xl p-3 text-white shadow-lg"
                  style={{ backgroundColor: mod.color }}
                >
                  <mod.icon className="h-6 w-6" />
                </div>
                <CardTitle
                  className="text-lg font-bold"
                  style={{
                    background: `linear-gradient(to right, var(--brand-primary), var(--brand-secondary))`,
                    WebkitBackgroundClip: "text",
                    WebkitTextFillColor: "transparent",
                    backgroundClip: "text",
                  }}
                >
                  {mod.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div
                  className="text-3xl font-extrabold drop-shadow-lg"
                  style={{ color: "var(--brand-secondary)" }}
                >
                  {mod.value ?? 0}
                </div>
                <p className="text-muted-foreground mt-2 text-base">
                  {mod.description}
                </p>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>

      {/* Live Vehicle Status Widget */}
      <div className="mt-4 rounded-2xl border-0 bg-gradient-to-br from-cyan-400/10 to-fuchsia-500/10 p-8 shadow-md">
        <h2 className="mb-4 bg-gradient-to-r from-fuchsia-500 to-cyan-400 bg-clip-text text-lg font-bold text-transparent">
          {t("dashboard.liveVehicles.title", {
            defaultValue: "Live Vehicle Status",
          })}
        </h2>
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          {/* Example vehicles, replace with real data */}
          {[
            {
              id: "V-101",
              location: "Warehouse A",
              speed: 45,
              status: "Moving",
            },
            { id: "V-102", location: "Gate 3", speed: 0, status: "Stopped" },
            { id: "V-103", location: "Highway", speed: 62, status: "Moving" },
          ].map((v) => (
            <div
              key={v.id}
              className="bg-background/80 flex flex-col gap-2 rounded-xl p-6 shadow transition-transform hover:scale-105"
            >
              <div className="flex items-center gap-2">
                <span
                  className="font-bold"
                  style={{ color: "var(--brand-secondary)" }}
                >
                  {v.id}
                </span>
                <span
                  className="rounded-full px-2 py-1 text-xs font-semibold"
                  style={{
                    backgroundColor:
                      v.status === "Moving"
                        ? "oklch(from var(--success) l c h / 0.2)"
                        : "oklch(from var(--warning) l c h / 0.2)",
                    color:
                      v.status === "Moving"
                        ? "var(--success-dark)"
                        : "var(--warning-dark)",
                  }}
                >
                  {v.status}
                </span>
              </div>
              <span className="text-muted-foreground text-sm">
                {t("dashboard.liveVehicles.location", { location: v.location })}
              </span>
              <span
                className="text-xs"
                style={{ color: "var(--brand-primary)" }}
              >
                {t("dashboard.liveVehicles.speed", { speed: v.speed })} km/h
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Top Performing Drivers Section */}
      <div className="mt-4 rounded-2xl border-0 bg-gradient-to-br from-fuchsia-500/10 to-cyan-400/10 p-8 shadow-md">
        <h2 className="mb-4 bg-gradient-to-r from-fuchsia-500 to-cyan-400 bg-clip-text text-lg font-bold text-transparent">
          {t("dashboard.topDrivers.title", {
            defaultValue: "Top Performing Drivers",
          })}
        </h2>
        <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
          {/* Example drivers, replace with real data */}
          {[
            { name: "John Doe", trips: 12, rating: 4.9 },
            { name: "Sarah Lee", trips: 10, rating: 4.8 },
            { name: "Ahmed Ali", trips: 9, rating: 4.7 },
          ].map((d) => (
            <div
              key={d.name}
              className="bg-background/80 flex flex-col items-center gap-2 rounded-xl p-6 shadow transition-transform hover:scale-105"
            >
              <div className="flex size-10 items-center justify-center rounded-full bg-gradient-to-br from-fuchsia-500 to-cyan-400 text-lg font-bold text-white">
                {d.name.charAt(0)}
              </div>
              <span className="font-semibold text-cyan-500">{d.name}</span>
              <span className="text-muted-foreground text-xs">
                {t("dashboard.topDrivers.trips", { trips: d.trips })}
              </span>
              <span className="text-xs text-yellow-500">⭐ {d.rating}</span>
            </div>
          ))}
        </div>
      </div>

      {/* System Health Overview Section */}
      <div className="mt-4 rounded-2xl border-0 bg-gradient-to-br from-cyan-400/10 to-fuchsia-500/10 p-8 shadow-md">
        <h2 className="mb-4 bg-gradient-to-r from-fuchsia-500 to-cyan-400 bg-clip-text text-lg font-bold text-transparent">
          {t("dashboard.systemHealth.title", {
            defaultValue: "System Health Overview",
          })}
        </h2>
        <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
          {/* Example health stats, replace with real data */}
          {[
            {
              label: t("dashboard.systemHealth.api"),
              status: "Online",
              color: "bg-green-500/20 text-green-700",
            },
            {
              label: t("dashboard.systemHealth.db"),
              status: "Online",
              color: "bg-green-500/20 text-green-700",
            },
            {
              label: t("dashboard.systemHealth.gps"),
              status: "Degraded",
              color: "bg-orange-500/20 text-orange-700",
            },
          ].map((s) => (
            <div
              key={s.label}
              className="bg-background/80 flex flex-col items-center gap-2 rounded-xl p-6 shadow"
            >
              <span
                className={`rounded-full px-3 py-1 text-xs font-semibold ${s.color}`}
              >
                {s.status}
              </span>
              <span className="font-semibold text-cyan-500">{s.label}</span>
            </div>
          ))}
        </div>
      </div>
      {/* Map Overview Section */}
      <div className="mt-4 grid grid-cols-1 gap-8 lg:grid-cols-3">
        <Card className="rounded-2xl border-0 bg-gradient-to-br from-cyan-400/10 to-fuchsia-500/10 shadow-md lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 bg-gradient-to-r from-fuchsia-500 to-cyan-400 bg-clip-text text-xl font-bold text-transparent">
              <MapPin className="h-6 w-6 animate-bounce text-cyan-500" />
              {t("dashboard.map.title")}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-muted flex h-64 items-center justify-center rounded-xl shadow-inner">
              <div className="text-center">
                <MapPin className="text-muted-foreground mx-auto mb-2 h-14 w-14 animate-pulse" />
                <p className="text-muted-foreground text-lg font-semibold">
                  {t("dashboard.map.placeholderTitle")}
                </p>
                <p className="text-muted-foreground mt-2 text-base">
                  {t("dashboard.map.placeholderSubtitle")}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="rounded-2xl border-0 bg-gradient-to-br from-fuchsia-500/10 to-cyan-400/10 shadow-md">
          <CardHeader>
            <CardTitle className="bg-gradient-to-r from-fuchsia-500 to-cyan-400 bg-clip-text text-xl font-bold text-transparent">
              {t("dashboard.recentActivity.title")}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* Recent Activity Items */}
              <div className="flex items-center gap-3">
                <div className="h-3 w-3 animate-pulse rounded-full bg-green-500"></div>
                <div className="flex-1">
                  <p className="text-base font-semibold text-cyan-500">
                    {t("dashboard.recentActivity.trailerEntered", {
                      id: "T-001",
                      area: "A-5",
                    })}
                  </p>
                  <p className="text-muted-foreground text-xs">
                    {t("dashboard.recentActivity.ago", { time: "2 minutes" })}
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <div
                  className="h-3 w-3 animate-pulse rounded-full"
                  style={{ backgroundColor: "var(--info)" }}
                ></div>
                <div className="flex-1">
                  <p
                    className="text-base font-semibold"
                    style={{ color: "var(--brand-secondary)" }}
                  >
                    {t("dashboard.recentActivity.driverShift", {
                      name: "John Doe",
                    })}
                  </p>
                  <p className="text-muted-foreground text-xs">
                    {t("dashboard.recentActivity.ago", { time: "15 minutes" })}
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <div
                  className="h-3 w-3 animate-pulse rounded-full"
                  style={{ backgroundColor: "var(--warning)" }}
                ></div>
                <div className="flex-1">
                  <p
                    className="text-base font-semibold"
                    style={{ color: "var(--brand-secondary)" }}
                  >
                    {t("dashboard.recentActivity.newArea", {
                      name: "Warehouse B",
                    })}
                  </p>
                  <p className="text-muted-foreground text-xs">
                    {t("dashboard.recentActivity.ago", { time: "1 hour" })}
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <div
                  className="h-3 w-3 animate-pulse rounded-full"
                  style={{ backgroundColor: "var(--error)" }}
                ></div>
                <div className="flex-1">
                  <p
                    className="text-base font-semibold"
                    style={{ color: "var(--brand-secondary)" }}
                  >
                    {t("dashboard.recentActivity.trailerLeft", { id: "T-012" })}
                  </p>
                  <p className="text-muted-foreground text-xs">
                    {t("dashboard.recentActivity.ago", { time: "2 hours" })}
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
