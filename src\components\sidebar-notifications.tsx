import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useSidebar } from "@/components/ui/sidebar";
import { Bell, Check } from "lucide-react";
import { useState } from "react";

export function SidebarNotifications() {
  const { state } = useSidebar();
  const [notifications, setNotifications] = useState([
    {
      id: 1,
      title: "New message received",
      description: "You have a new message from <PERSON>",
      time: "2 min ago",
      read: false,
      type: "message",
    },
    {
      id: 2,
      title: "System update available",
      description: "A new system update is ready to install",
      time: "1 hour ago",
      read: false,
      type: "system",
    },
    {
      id: 3,
      title: "Task completed",
      description: "Your export task has been completed successfully",
      time: "3 hours ago",
      read: true,
      type: "success",
    },
  ]);

  const unreadCount = notifications.filter((n) => !n.read).length;

  const markAsRead = (id: number) => {
    setNotifications((prev) =>
      prev.map((n) => (n.id === id ? { ...n, read: true } : n)),
    );
  };

  const markAllAsRead = () => {
    setNotifications((prev) => prev.map((n) => ({ ...n, read: true })));
  };

  // For header usage (compact button)
  const isHeaderMode = !state || state === "collapsed";

  if (isHeaderMode) {
    return (
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className="from-brand-secondary/10 to-brand-primary/10 hover:from-brand-secondary/20 hover:to-brand-primary/20 relative h-8 w-8 rounded-lg bg-gradient-to-r p-0"
          >
            <Bell className="text-brand-secondary h-4 w-4" />
            {unreadCount > 0 && (
              <Badge className="from-brand-primary to-brand-secondary absolute -top-1 -right-1 h-4 w-4 rounded-full bg-gradient-to-r p-0 text-xs text-white">
                {unreadCount > 9 ? "9+" : unreadCount}
              </Badge>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent align="center" side="right" className="w-80">
          <NotificationContent
            notifications={notifications}
            markAsRead={markAsRead}
            markAllAsRead={markAllAsRead}
            unreadCount={unreadCount}
          />
        </PopoverContent>
      </Popover>
    );
  }

  // For sidebar content usage (full width button)
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          className="hover:from-brand-secondary/5 hover:to-brand-primary/5 w-full justify-between rounded-xl p-3 text-left hover:bg-gradient-to-r"
        >
          <div className="flex items-center gap-2">
            <Bell className="text-brand-secondary h-4 w-4" />
            <span className="text-sm font-medium">Notifications</span>
          </div>
          {unreadCount > 0 && (
            <Badge className="from-brand-primary to-brand-secondary bg-gradient-to-r text-white">
              {unreadCount}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent align="start" side="right" className="w-80">
        <NotificationContent
          notifications={notifications}
          markAsRead={markAsRead}
          markAllAsRead={markAllAsRead}
          unreadCount={unreadCount}
        />
      </PopoverContent>
    </Popover>
  );
}

function NotificationContent({
  notifications,
  markAsRead,
  markAllAsRead,
  unreadCount,
}: {
  notifications: any[];
  markAsRead: (id: number) => void;
  markAllAsRead: () => void;
  unreadCount: number;
}) {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="from-brand-primary to-brand-secondary bg-gradient-to-r bg-clip-text font-semibold text-transparent">
          Notifications
        </h3>
        {unreadCount > 0 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={markAllAsRead}
            className="text-brand-secondary hover:from-brand-secondary/10 hover:to-brand-primary/10 text-xs hover:bg-gradient-to-r"
          >
            Mark all read
          </Button>
        )}
      </div>

      <ScrollArea className="h-64">
        <div className="space-y-2">
          {notifications.map((notification) => (
            <div
              key={notification.id}
              className={`rounded-lg border p-3 transition-colors ${
                notification.read
                  ? "bg-muted/30"
                  : "border-brand-secondary/20 from-brand-secondary/5 to-brand-primary/5 bg-gradient-to-r"
              }`}
            >
              <div className="flex items-start justify-between gap-2">
                <div className="flex-1 space-y-1">
                  <p className="text-brand-secondary text-sm font-medium">
                    {notification.title}
                  </p>
                  <p className="text-muted-foreground text-xs">
                    {notification.description}
                  </p>
                  <p className="text-muted-foreground text-xs">
                    {notification.time}
                  </p>
                </div>
                {!notification.read && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => markAsRead(notification.id)}
                    className="hover:from-brand-secondary/20 hover:to-brand-primary/20 h-6 w-6 rounded-full p-0 hover:bg-gradient-to-r"
                  >
                    <Check className="text-brand-secondary h-3 w-3" />
                  </Button>
                )}
              </div>
            </div>
          ))}
        </div>
      </ScrollArea>
    </div>
  );
}
