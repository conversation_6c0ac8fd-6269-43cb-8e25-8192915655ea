export const driversTranslations = {
  title: "Drivers",
  add: "Add Driver",

  confirm_delete: {
    deleteDriverTitle: "Delete Driver",
    deleteDriverDescription:
      "Are you sure you want to delete this driver? This action cannot be undone.",
  },

  dialog: {
    createTitle: "Create Driver",
    createDescription: "Fill in the details to create a new driver.",
    editTitle: "Edit Driver",
    editDescription: "Update the driver details below.",
  },

  form: {
    namePlaceholder: "Driver name",
    driverType: "Driver Type",
    driverTypePlaceholder: "Driver type",
    employee: "Employee",
    subcontractor: "Subcontractor",
    nationalId: "National ID",
    nationalIdPlaceholder: "National ID",
    nationality: "Nationality",
    nationalityPlaceholder: "Nationality",
    identityNumber: "Identity Number",
    identityNumberPlaceholder: "Identity number",
    address: "Address",
    addressPlaceholder: "Driver's address",
    isActive: "Active",
    isActiveDescription: "Is this driver active?",
    no_driver_type_found: "No driver type found",
  },

  actions: {
    create: "Create",
    creating: "Creating...",
    update: "Update Driver",
    updating: "Updating...",
  },

  validation: {
    name: "Name is required",
    driverType: "Driver type is required",
    nationalId: "National ID is required",
    nationality: "Nationality is required",
    identityNumber: "Identity number is required",
    address: "Address is required",
  },

  table: {
    selectAll: "Select all",
    selectRow: "Select row",
    isActive: "Is Active",
  },

  actions: {
    title: "Actions",
  },
};
