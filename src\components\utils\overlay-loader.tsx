import { useTranslation } from "react-i18next";

export function OverlayLoader({ inCenter = true }: { inCenter?: boolean }) {
  const { t } = useTranslation();

  return (
    <div
      className={`flex flex-col items-center justify-center gap-4 py-10 ${
        inCenter
          ? "bg-background/80 fixed inset-0 z-50 backdrop-blur-sm"
          : "relative"
      }`}
    >
      {/* Modern animated loader */}
      <div className="relative">
        {/* Outer rotating ring */}
        <div className="h-12 w-12 animate-spin rounded-full border-2 border-transparent bg-gradient-to-r from-fuchsia-500 to-cyan-400">
          <div className="bg-background m-0.5 h-full w-full rounded-full"></div>
        </div>

        {/* Inner pulsing dot */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="h-3 w-3 animate-pulse rounded-full bg-gradient-to-r from-fuchsia-500 to-cyan-400"></div>
        </div>

        {/* Glow effect */}
        <div className="absolute inset-0 h-12 w-12 animate-ping rounded-full bg-gradient-to-r from-fuchsia-500/20 to-cyan-400/20"></div>
      </div>

      {/* Loading text with gradient */}
      <div className="flex flex-col items-center gap-2">
        <p className="bg-gradient-to-r from-fuchsia-500 to-cyan-400 bg-clip-text text-sm font-medium text-transparent">
          {t("common.loading.default")}
        </p>

        {/* Animated dots */}
        <div className="flex gap-1">
          <div className="h-1.5 w-1.5 animate-bounce rounded-full bg-fuchsia-500/60 [animation-delay:-0.3s]"></div>
          <div className="h-1.5 w-1.5 animate-bounce rounded-full bg-fuchsia-500/60 [animation-delay:-0.15s]"></div>
          <div className="h-1.5 w-1.5 animate-bounce rounded-full bg-cyan-400/60"></div>
        </div>
      </div>
    </div>
  );
}
