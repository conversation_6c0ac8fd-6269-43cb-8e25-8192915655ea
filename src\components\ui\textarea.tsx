import * as React from "react";

import { cn } from "@/lib/utils";

function Textarea({ className, ...props }: React.ComponentProps<"textarea">) {
  return (
    <textarea
      data-slot="textarea"
      className={cn(
        // Base styles
        "flex field-sizing-content min-h-24 w-full resize-none rounded-xl border px-4 py-3 text-base transition-all duration-300 outline-none",
        // Glass morphism background
        "border-white/10 bg-gradient-to-br from-slate-900/95 via-slate-800/90 to-slate-900/95 backdrop-blur-2xl",
        // Placeholder and text
        "text-slate-200 placeholder:text-slate-400",
        "selection:bg-cyan-400/20 selection:text-cyan-100",
        // Focus states
        "focus:border-cyan-400/50 focus:bg-gradient-to-br focus:from-slate-800/95 focus:via-slate-700/90 focus:to-slate-800/95",
        "focus:shadow-lg focus:ring-2 focus:shadow-cyan-400/10 focus:ring-cyan-400/20",
        // Hover states
        "hover:border-white/20 hover:bg-gradient-to-br hover:from-slate-800/95 hover:via-slate-700/90 hover:to-slate-800/95 hover:shadow-md",
        // Invalid states
        "aria-invalid:border-red-400/50 aria-invalid:bg-red-500/5 aria-invalid:ring-red-400/20",
        // Disabled states
        "disabled:cursor-not-allowed disabled:bg-slate-800/30 disabled:text-slate-500 disabled:opacity-50",
        // Responsive text size
        "md:text-sm",
        className,
      )}
      {...props}
    />
  );
}

export { Textarea };
