import * as React from "react";

import { cn } from "@/lib/utils";

function Textarea({ className, ...props }: React.ComponentProps<"textarea">) {
  return (
    <textarea
      data-slot="textarea"
      className={cn(
        // Base styles - simplified and clean
        "flex min-h-20 w-full resize-none rounded-lg border px-3 py-2 text-sm transition-colors duration-200 outline-none",
        // Simple background and border
        "border-white/20 bg-white/5 backdrop-blur-sm",
        // Text colors
        "text-slate-100 placeholder:text-slate-400",
        // Focus states - simple brand color accent
        "focus:border-[var(--brand-secondary)] focus:bg-white/10 focus:ring-1 focus:ring-[var(--brand-secondary)]/20",
        // Hover states
        "hover:border-white/30 hover:bg-white/8",
        // Invalid states
        "aria-invalid:border-red-400 aria-invalid:focus:border-red-400 aria-invalid:focus:ring-red-400/20",
        // Disabled states
        "disabled:cursor-not-allowed disabled:bg-white/2 disabled:opacity-50",
        className,
      )}
      {...props}
    />
  );
}

export { Textarea };
