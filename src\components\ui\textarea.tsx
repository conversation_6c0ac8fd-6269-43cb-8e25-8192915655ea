import * as React from "react";

import { cn } from "@/lib/utils";

function Textarea({ className, ...props }: React.ComponentProps<"textarea">) {
  return (
    <textarea
      data-slot="textarea"
      className={cn(
        // Base styles
        "bg-background/50 flex field-sizing-content min-h-20 w-full resize-none rounded-lg border px-4 py-3 text-base transition-all duration-200 outline-none",
        // Border and background
        "border-border/50 backdrop-blur-sm",
        "dark:bg-input/20 dark:border-input/30",
        // Placeholder and text
        "placeholder:text-muted-foreground/70 text-foreground",
        "selection:bg-primary/20 selection:text-primary-foreground",
        // Focus states with gradient ring
        "focus:bg-background/80 focus:border-transparent",
        "focus:ring-gradient-to-r focus:ring-2 focus:ring-fuchsia-500/30",
        "focus:shadow-lg focus:shadow-fuchsia-500/10",
        // Hover states
        "hover:border-border hover:bg-background/70 hover:shadow-md",
        // Invalid states
        "aria-invalid:border-destructive/50 aria-invalid:ring-destructive/20",
        "dark:aria-invalid:ring-destructive/30 aria-invalid:bg-destructive/5",
        // Disabled states
        "disabled:bg-muted/30 disabled:cursor-not-allowed disabled:opacity-50",
        // Responsive text size
        "md:text-sm",
        className,
      )}
      {...props}
    />
  );
}

export { Textarea };
