import {
  AreaChart,
  BookOpen,
  GalleryVerticalEnd,
  LayoutDashboard,
  Search,
  Sparkles,
  Tractor,
  Train,
  University,
  X,
} from "lucide-react";
import * as React from "react";

import { localeAtom } from "@/atoms/app/locale-atom";
import { NavMain } from "@/components/nav-main";
import { But<PERSON> } from "@/components/ui/button";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
  useSidebar,
} from "@/components/ui/sidebar";
import { cn } from "@/lib/utils";
import { URLS } from "@/utils/urls";
import { useTranslation } from "react-i18next";

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { locale_code } = localeAtom.useValue();
  const { t } = useTranslation();
  const { isMobile, state, setOpenMobile } = useSidebar();
  const [searchQuery, setSearchQuery] = React.useState("");
  const [isSearchFocused, setIsSearchFocused] = React.useState(false);

  // Handle escape key to close mobile sidebar
  // React.useEffect(() => {
  //   const handleEscape = (e: KeyboardEvent) => {
  //     if (e.key === "Escape" && isMobile) {
  //       setOpenMobile(false);
  //     }
  //   };

  //   document.addEventListener("keydown", handleEscape);
  //   return () => document.removeEventListener("keydown", handleEscape);
  // }, [isMobile, setOpenMobile]);

  const navItems = React.useMemo(
    () => [
      {
        title: t("dashboard.title"),
        url: URLS.dashboard,
        icon: LayoutDashboard,
        badge: "New",
      },
      {
        title: t("units.header.title"),
        url: URLS.units,
        icon: University,
      },
      {
        title: t("areas.title"),
        url: URLS.areas,
        icon: AreaChart,
      },
      {
        title: t("trailers.title"),
        url: URLS.trailers,
        icon: Train,
      },
      {
        title: t("tracking.title"),
        url: URLS.tracking,
        icon: Tractor,
        badge: "Live",
      },
      {
        title: t("ui.common.users-management"),
        url: "#",
        icon: BookOpen,
        items: [
          {
            title: t("drivers.title"),
            url: URLS.drivers,
          },
          {
            title: t("users.header.title"),
            url: URLS.users,
          },
        ],
      },
    ],
    [t],
  );

  return (
    <Sidebar
      collapsible={isMobile ? "offcanvas" : "icon"}
      {...props}
      variant={isMobile ? "sidebar" : "floating"}
      side={locale_code === "ar" ? "right" : "left"}
      className={cn(
        "border-none shadow-2xl backdrop-blur-2xl transition-all duration-300",
        // Glass morphism background
        "bg-gradient-to-br from-slate-900/95 via-slate-800/90 to-slate-900/95",
        // Border and ring effects
        "ring-1 ring-white/10",
        // Mobile responsive adjustments
        isMobile && "w-72",
        // Desktop responsive adjustments
        !isMobile && "w-64 group-data-[collapsible=icon]:w-16",
      )}
    >
      {/* Modern Header */}
      <SidebarHeader className="border-b border-white/10 p-4 group-data-[collapsible=icon]:p-2">
        {/* Mobile Header with Close Button */}
        {isMobile && (
          <div className="mb-4 flex items-center justify-between">
            <span className="text-lg font-bold text-slate-200">
              {t("ui.sidebar.menu")}
            </span>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setOpenMobile(false)}
              className="h-8 w-8 rounded-lg border border-white/10 bg-gradient-to-br from-white/10 to-white/5 p-0 hover:bg-gradient-to-br hover:from-cyan-400/20 hover:to-blue-500/20"
            >
              <X className="h-4 w-4 text-slate-300" />
            </Button>
          </div>
        )}

        {/* Modern Brand Section */}
        <div className="mb-4 flex items-center gap-3 rounded-2xl border border-white/10 bg-gradient-to-br from-white/10 to-white/5 p-3 backdrop-blur-sm group-data-[collapsible=icon]:mb-0 group-data-[collapsible=icon]:justify-center group-data-[collapsible=icon]:rounded-full group-data-[collapsible=icon]:border-0 group-data-[collapsible=icon]:bg-transparent group-data-[collapsible=icon]:p-0">
          <div className="flex size-10 items-center justify-center rounded-xl bg-gradient-to-br from-cyan-400 to-blue-500 shadow-lg group-data-[collapsible=icon]:size-12 group-data-[collapsible=icon]:rounded-full">
            <GalleryVerticalEnd className="size-5 text-white group-data-[collapsible=icon]:size-6" />
          </div>
          <div className="flex flex-col group-data-[collapsible=icon]:hidden">
            <span className="text-sm font-bold text-slate-200">
              {t("ui.app.name")}
            </span>
            <span className="text-xs text-slate-400">
              {t("ui.app.version")}
            </span>
          </div>
        </div>

        {/* Modern Search */}
        <div className="group-data-[collapsible=icon]:hidden">
          <div className="relative">
            <Search
              className={cn(
                "absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-slate-400 transition-colors",
                isSearchFocused && "text-cyan-400",
              )}
            />
            <input
              type="text"
              placeholder={t("ui.sidebar.search")}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onFocus={() => setIsSearchFocused(true)}
              onBlur={() => setIsSearchFocused(false)}
              className={cn(
                "w-full rounded-xl border border-white/10 bg-gradient-to-br from-white/5 to-white/2 py-2.5 pr-4 pl-10 text-sm text-slate-200 backdrop-blur-sm transition-all placeholder:text-slate-400 focus:border-cyan-400/50 focus:bg-gradient-to-br focus:from-white/10 focus:to-white/5 focus:ring-2 focus:ring-cyan-400/20 focus:outline-none",
                isSearchFocused && "shadow-lg shadow-cyan-400/10",
              )}
            />
            {searchQuery && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSearchQuery("")}
                className="absolute top-1/2 right-2 h-6 w-6 -translate-y-1/2 rounded-md p-0 hover:bg-white/10"
              >
                <X className="h-3 w-3 text-slate-400" />
              </Button>
            )}
          </div>
        </div>
      </SidebarHeader>

      {/* Navigation Content */}
      <SidebarContent className="px-2 py-4">
        <NavMain
          items={navItems}
          searchQuery={searchQuery}
          isCollapsed={!isMobile && state === "collapsed"}
          onNavigate={() => {
            if (isMobile) {
              setOpenMobile(false);
            }
          }}
        />
      </SidebarContent>

      {/* Modern Footer - Show on mobile when open, hide on desktop when collapsed */}
      {(isMobile || (!isMobile && state === "expanded")) && (
        <SidebarFooter className="border-t border-white/10 p-4">
          {/* Modern Pro Upgrade Banner */}
          <div className="rounded-xl border border-amber-500/20 bg-gradient-to-br from-amber-500/10 to-orange-500/10 p-4 backdrop-blur-sm group-data-[collapsible=icon]:hidden">
            <div className="mb-2 flex items-center gap-2">
              <Sparkles className="h-4 w-4 text-amber-400" />
              <span className="text-sm font-medium text-amber-300">
                {t("ui.sidebar.upgradeToPro")}
              </span>
            </div>
            <p className="mb-3 text-xs text-slate-400">
              {t("ui.sidebar.unlockFeatures")}
            </p>
            <Button
              size="sm"
              className="h-8 w-full bg-gradient-to-r from-amber-500 to-orange-500 text-xs font-medium text-white shadow-lg transition-all duration-300 hover:from-amber-600 hover:to-orange-600 hover:shadow-amber-500/20"
            >
              {t("ui.sidebar.upgradeNow")}
            </Button>
          </div>
        </SidebarFooter>
      )}

      <SidebarRail />
    </Sidebar>
  );
}
