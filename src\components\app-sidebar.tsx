import {
  AreaChart,
  BookOpen,
  GalleryVerticalEnd,
  LayoutDashboard,
  Search,
  Sparkles,
  Tractor,
  Train,
  University,
  X,
} from "lucide-react";
import * as React from "react";

import { localeAtom } from "@/atoms/app/locale-atom";
import { NavMain } from "@/components/nav-main";
import { But<PERSON> } from "@/components/ui/button";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
  useSidebar,
} from "@/components/ui/sidebar";
import { cn } from "@/lib/utils";
import { URLS } from "@/utils/urls";
import { useTranslation } from "react-i18next";

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { locale_code } = localeAtom.useValue();
  const { t } = useTranslation();
  const { isMobile, state, setOpenMobile } = useSidebar();
  const [searchQuery, setSearchQuery] = React.useState("");
  const [isSearchFocused, setIsSearchFocused] = React.useState(false);

  // Handle escape key to close mobile sidebar
  // React.useEffect(() => {
  //   const handleEscape = (e: KeyboardEvent) => {
  //     if (e.key === "Escape" && isMobile) {
  //       setOpenMobile(false);
  //     }
  //   };

  //   document.addEventListener("keydown", handleEscape);
  //   return () => document.removeEventListener("keydown", handleEscape);
  // }, [isMobile, setOpenMobile]);

  const navItems = React.useMemo(
    () => [
      {
        title: t("dashboard.title"),
        url: URLS.dashboard,
        icon: LayoutDashboard,
        badge: "New",
      },
      {
        title: t("units.header.title"),
        url: URLS.units,
        icon: University,
      },
      {
        title: t("areas.title"),
        url: URLS.areas,
        icon: AreaChart,
      },
      {
        title: t("trailers.title"),
        url: URLS.trailers,
        icon: Train,
      },
      {
        title: t("tracking.title"),
        url: URLS.tracking,
        icon: Tractor,
        badge: "Live",
      },
      {
        title: t("ui.common.users-management"),
        url: "#",
        icon: BookOpen,
        items: [
          {
            title: t("drivers.title"),
            url: URLS.drivers,
          },
          {
            title: t("users.header.title"),
            url: URLS.users,
          },
        ],
      },
    ],
    [t],
  );

  return (
    <Sidebar
      collapsible={isMobile ? "offcanvas" : "icon"}
      {...props}
      variant={isMobile ? "sidebar" : "floating"}
      side={locale_code === "ar" ? "right" : "left"}
      className={cn(
        "from-brand-secondary/10 to-brand-primary/10 ring-brand-secondary/20 border-none bg-gradient-to-b shadow-2xl ring-1 backdrop-blur-xl transition-all duration-300",
        // Mobile responsive adjustments
        isMobile && "w-72",
        // Desktop responsive adjustments
        !isMobile && "w-64 group-data-[collapsible=icon]:w-16",
      )}
    >
      {/* Enhanced Header */}
      <SidebarHeader className="border-brand-secondary/20 border-b p-3 group-data-[collapsible=icon]:p-2 md:p-4 group-data-[collapsible=icon]:md:p-1">
        {/* Mobile Header with Close Button */}
        {isMobile && (
          <div className="mb-3 flex items-center justify-between">
            <span className="from-brand-primary to-brand-secondary bg-gradient-to-r bg-clip-text text-lg font-bold text-transparent">
              {t("ui.sidebar.menu")}
            </span>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setOpenMobile(false)}
              className="from-brand-secondary/10 to-brand-primary/10 hover:from-brand-secondary/20 hover:to-brand-primary/20 h-8 w-8 rounded-lg bg-gradient-to-r p-0"
            >
              <X className="text-brand-secondary h-4 w-4" />
            </Button>
          </div>
        )}

        {/* Brand Section */}
        <div className="from-brand-secondary/10 to-brand-primary/10 ring-brand-secondary/20 mb-3 flex items-center gap-3 rounded-2xl bg-gradient-to-r p-3 shadow-sm ring-1 group-data-[collapsible=icon]:mb-0 group-data-[collapsible=icon]:justify-center group-data-[collapsible=icon]:rounded-full group-data-[collapsible=icon]:bg-transparent group-data-[collapsible=icon]:p-0 group-data-[collapsible=icon]:shadow-none group-data-[collapsible=icon]:ring-0 md:mb-4">
          <div className="from-brand-primary to-brand-secondary flex size-8 items-center justify-center rounded-xl bg-gradient-to-br shadow-lg group-data-[collapsible=icon]:size-10 group-data-[collapsible=icon]:rounded-full md:size-10">
            <GalleryVerticalEnd className="size-4 text-white group-data-[collapsible=icon]:size-5 md:size-5" />
          </div>
          <div className="flex flex-col group-data-[collapsible=icon]:hidden">
            <span className="from-brand-primary to-brand-secondary bg-gradient-to-r bg-clip-text text-sm font-bold text-transparent">
              {t("ui.app.name")}
            </span>
            <span className="text-muted-foreground text-xs">
              {t("ui.app.version")}
            </span>
          </div>
        </div>

        {/* Enhanced Search */}
        <div className="group-data-[collapsible=icon]:hidden">
          <div className="relative">
            <Search
              className={cn(
                "text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transition-colors",
                isSearchFocused && "text-cyan-500",
              )}
            />
            <input
              type="text"
              placeholder={t("ui.sidebar.search")}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onFocus={() => setIsSearchFocused(true)}
              onBlur={() => setIsSearchFocused(false)}
              className={cn(
                "bg-background/50 placeholder:text-muted-foreground focus:bg-background/80 w-full rounded-xl border border-cyan-400/30 py-2 pr-4 pl-10 text-sm shadow-sm ring-1 ring-transparent transition-all focus:border-cyan-400/50 focus:ring-2 focus:ring-cyan-400/20 focus:outline-none md:py-2.5",
                isSearchFocused && "shadow-md",
              )}
            />
            {searchQuery && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSearchQuery("")}
                className="hover:bg-muted absolute top-1/2 right-2 h-6 w-6 -translate-y-1/2 rounded-md p-0"
              >
                <X className="h-3 w-3" />
              </Button>
            )}
          </div>
        </div>
      </SidebarHeader>

      {/* Navigation Content */}
      <SidebarContent className="px-2 py-4">
        <NavMain
          items={navItems}
          searchQuery={searchQuery}
          isCollapsed={!isMobile && state === "collapsed"}
          onNavigate={() => {
            if (isMobile) {
              setOpenMobile(false);
            }
          }}
        />
      </SidebarContent>

      {/* Enhanced Footer - Show on mobile when open, hide on desktop when collapsed */}
      {(isMobile || (!isMobile && state === "expanded")) && (
        <SidebarFooter className="border-t border-cyan-400/20 p-3 md:p-4">
          {/* Pro Upgrade Banner */}
          <div className="mt-3 rounded-xl bg-gradient-to-r from-amber-500/10 to-orange-500/10 p-3 ring-1 ring-amber-500/20 group-data-[collapsible=icon]:hidden md:mt-4">
            <div className="flex items-center gap-2">
              <Sparkles className="h-4 w-4 text-amber-500" />
              <span className="text-xs font-medium text-amber-600 dark:text-amber-400">
                {t("ui.sidebar.upgradeToPro")}
              </span>
            </div>
            <p className="text-muted-foreground mt-1 text-xs">
              {t("ui.sidebar.unlockFeatures")}
            </p>
            <Button
              size="sm"
              className="mt-2 h-7 w-full bg-gradient-to-r from-amber-500 to-orange-500 text-xs font-medium text-white hover:from-amber-600 hover:to-orange-600"
            >
              {t("ui.sidebar.upgradeNow")}
            </Button>
          </div>
        </SidebarFooter>
      )}

      <SidebarRail />
    </Sidebar>
  );
}
