import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import i18n from "@/localization/i18n";
import { Bell, Search } from "lucide-react";
import { useState } from "react";
import { useTranslation } from "react-i18next";

type Notification = {
  id: number;
  title: string;
  description: string;
  read: boolean;
  date: string;
};

export default function NotificationsPage() {
  const { t } = useTranslation();
  const [search, setSearch] = useState("");

  const [notifications, setNotifications] = useState<Notification[]>([
    {
      id: 1,
      title: "New Message",
      description: "You received a new message from <PERSON>.",
      read: false,
      date: "2025-09-20 10:15 AM",
    },
    {
      id: 2,
      title: "System Update",
      description: "Your account has been updated successfully.",
      read: true,
      date: "2025-09-19 5:30 PM",
    },
    {
      id: 3,
      title: "Reminder",
      description: "Meeting at 3 PM today.",
      read: false,
      date: "2025-09-18 8:00 AM",
    },
  ]);

  const markAsRead = (id: number) => {
    setNotifications((prev) =>
      prev.map((n) => (n.id === id ? { ...n, read: true } : n)),
    );
  };

  const markAllAsRead = () => {
    setNotifications((prev) => prev.map((n) => ({ ...n, read: true })));
  };

  const filteredNotifications = notifications.filter(
    (n) =>
      n.title.toLowerCase().includes(search.toLowerCase()) ||
      n.description.toLowerCase().includes(search.toLowerCase()),
  );

  return (
    <div
      dir={i18n.language === "ar" ? "rtl" : "ltr"}
      className="container mx-auto p-8"
    >
      <Card className="via-background/80 rounded-2xl border-0 bg-gradient-to-br from-fuchsia-500/10 to-cyan-400/10 shadow-2xl">
        {/* Header */}
        <CardHeader className="flex flex-col gap-4 rounded-t-2xl bg-gradient-to-r from-cyan-400/10 to-fuchsia-500/10 px-6 py-4 sm:flex-row sm:items-center sm:justify-between">
          <div className="flex items-center gap-3">
            <Bell className="h-6 w-6 animate-bounce text-cyan-500" />
            <CardTitle className="bg-gradient-to-r from-fuchsia-500 to-cyan-400 bg-clip-text text-xl font-bold text-transparent">
              {t("ui.notifications.notifications")}
            </CardTitle>
          </div>

          {notifications.some((n) => !n.read) && (
            <Button
              size="sm"
              variant="outline"
              onClick={markAllAsRead}
              className="rounded-full border-cyan-400/40 bg-gradient-to-r from-cyan-400/10 to-fuchsia-500/10 font-semibold whitespace-nowrap text-cyan-500 shadow transition-transform hover:scale-105"
            >
              {t("ui.notifications.markAllAsRead")}
            </Button>
          )}
        </CardHeader>

        {/* Search */}
        <div className="px-8 pb-4">
          <div className="relative">
            <Search
              className={`absolute top-2.5 h-4 w-4 ${
                i18n.language === "ar" ? "right-2" : "left-2"
              }`}
              style={{ color: "var(--brand-secondary)" }}
            />
            <Input
              placeholder={
                t("ui.notifications.search") || "Search notifications..."
              }
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className={`bg-background/80 rounded-xl shadow-inner transition focus:outline-none ${
                i18n.language === "ar" ? "pr-8" : "pl-8"
              }`}
              style={{
                borderColor: "oklch(from var(--brand-secondary) l c h / 0.2)",
                ":focus": {
                  "--tw-ring-color": "var(--brand-primary)",
                  "--tw-ring-width": "2px",
                },
              }}
            />
          </div>
        </div>

        {/* Notifications List */}
        <CardContent className="pb-8">
          {filteredNotifications.length === 0 ? (
            <p className="text-muted-foreground py-8 text-center text-lg">
              {t("ui.notifications.noNotifications")}
            </p>
          ) : (
            <div className="space-y-6">
              {filteredNotifications.map((n) => (
                <div
                  key={n.id}
                  className={`cursor-pointer rounded-xl border-0 p-6 shadow-md transition-all ${
                    n.read
                      ? "from-background/60 to-muted/30 hover:to-muted/50 bg-gradient-to-r"
                      : "bg-gradient-to-r from-cyan-400/10 to-fuchsia-500/10 hover:from-cyan-400/20 hover:to-fuchsia-500/20"
                  }`}
                  onClick={() => markAsRead(n.id)}
                >
                  <div className="flex items-center justify-between gap-4">
                    <h4 className="text-lg font-semibold text-cyan-500">
                      {n.title}
                    </h4>
                    <Badge
                      variant={n.read ? "secondary" : "default"}
                      className={`rounded-full px-3 py-1 text-[10px] font-bold ${
                        n.read
                          ? "bg-muted/40 text-muted-foreground"
                          : "animate-pulse bg-gradient-to-r from-fuchsia-500 to-cyan-400 text-white"
                      }`}
                    >
                      {n.read
                        ? t("ui.notifications.read")
                        : t("ui.notifications.unread")}
                    </Badge>
                  </div>
                  <p className="text-muted-foreground mt-2 text-base">
                    {n.description}
                  </p>
                  <span className="mt-2 block text-xs text-gray-400">
                    {n.date}
                  </span>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
