import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";
import * as React from "react";

import { cn } from "@/lib/utils";

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-offset-background",
  {
    variants: {
      variant: {
        default:
          "text-white font-semibold shadow-lg hover:shadow-xl hover:scale-[1.02] active:scale-[0.98] focus-visible:ring-[var(--focus-ring)]/30",
        destructive:
          "text-white font-semibold shadow-lg hover:shadow-xl hover:scale-[1.02] active:scale-[0.98] focus-visible:ring-[var(--error)]/30",
        outline:
          "border-2 border-border/50 bg-background/50 backdrop-blur-sm hover:shadow-md hover:scale-[1.02] active:scale-[0.98] text-foreground font-medium",
        secondary:
          "bg-gradient-to-r from-secondary to-secondary/80 text-secondary-foreground font-medium shadow-md hover:shadow-lg hover:scale-[1.02] hover:from-secondary/90 hover:to-secondary/70 active:scale-[0.98]",
        ghost:
          "hover:text-foreground hover:shadow-md hover:scale-[1.02] active:scale-[0.98] text-muted-foreground font-medium",
        link: "underline-offset-4 hover:underline transition-colors font-medium",
        gradient:
          "text-white font-semibold shadow-lg hover:shadow-xl hover:scale-[1.02] active:scale-[0.98]",
        "gradient-outline":
          "relative border-2 border-transparent text-foreground font-medium hover:scale-[1.02] active:scale-[0.98] hover:shadow-lg",
        "destructive-gradient":
          "text-white font-semibold shadow-lg hover:shadow-xl hover:scale-[1.02] active:scale-[0.98] focus-visible:ring-[var(--error)]/30",
      },
      size: {
        default: "h-10 px-6 py-2 has-[>svg]:px-4",
        sm: "h-8 rounded-lg gap-1.5 px-4 has-[>svg]:px-3 text-xs",
        lg: "h-12 rounded-lg px-8 has-[>svg]:px-6 text-base font-semibold",
        icon: "size-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  },
);

function Button({
  className,
  variant,
  size,
  asChild = false,
  style,
  ...props
}: React.ComponentProps<"button"> &
  VariantProps<typeof buttonVariants> & {
    asChild?: boolean;
  }) {
  const Comp = asChild ? Slot : "button";

  // Apply custom styles based on variant
  const getCustomStyles = (): React.CSSProperties => {
    const baseStyles = style || {};

    switch (variant) {
      case "default":
      case "gradient":
        return {
          ...baseStyles,
          background: `linear-gradient(to right, var(--button-gradient-from), var(--button-gradient-to))`,
        };
      case "destructive":
      case "destructive-gradient":
        return {
          ...baseStyles,
          background: `linear-gradient(to right, var(--error), var(--error-dark))`,
        };
      case "outline":
        return {
          ...baseStyles,
          background: `linear-gradient(to right,
            oklch(from var(--brand-primary) l c h / 0.1),
            oklch(from var(--brand-secondary) l c h / 0.1))`,
          borderColor: "oklch(from var(--brand-primary) l c h / 0.3)",
        };
      case "ghost":
        return {
          ...baseStyles,
          background: `linear-gradient(to right,
            oklch(from var(--brand-primary) l c h / 0.1),
            oklch(from var(--brand-secondary) l c h / 0.1))`,
        };
      case "link":
        return {
          ...baseStyles,
          color: "var(--link-color)",
        };
      case "gradient-outline":
        return {
          ...baseStyles,
          background: `linear-gradient(135deg,
            oklch(from var(--brand-primary) l c h / 0.1),
            oklch(from var(--brand-secondary) l c h / 0.1))`,
        };
      default:
        return baseStyles;
    }
  };

  return (
    <Comp
      data-slot="button"
      className={cn(
        "cursor-pointer",
        buttonVariants({ variant, size, className }),
      )}
      style={getCustomStyles()}
      {...props}
    />
  );
}

export { Button, buttonVariants };
