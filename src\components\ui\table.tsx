import * as React from "react";

import { cn } from "@/lib/utils";

function Table({ className, ...props }: React.ComponentProps<"table">) {
  return (
    <div
      data-slot="table-container"
      className="border-border/50 bg-background/50 relative w-full overflow-hidden rounded-xl border shadow-lg backdrop-blur-sm"
    >
      <div className="scroll-gradient overflow-x-auto">
        <table
          data-slot="table"
          className={cn("w-full caption-bottom text-sm", className)}
          {...props}
        />
      </div>
    </div>
  );
}

function TableHeader({ className, ...props }: React.ComponentProps<"thead">) {
  return (
    <thead
      data-slot="table-header"
      className={cn(
        "[&_tr]:border-border/30 bg-gradient-to-r from-fuchsia-500/5 to-cyan-400/5 [&_tr]:border-b",
        className,
      )}
      {...props}
    />
  );
}

function TableBody({ className, ...props }: React.ComponentProps<"tbody">) {
  return (
    <tbody
      data-slot="table-body"
      className={cn("[&_tr:last-child]:border-0", className)}
      {...props}
    />
  );
}

function TableFooter({ className, ...props }: React.ComponentProps<"tfoot">) {
  return (
    <tfoot
      data-slot="table-footer"
      className={cn(
        "from-muted/30 to-muted/20 border-border/30 border-t bg-gradient-to-r font-medium backdrop-blur-sm [&>tr]:last:border-b-0",
        className,
      )}
      {...props}
    />
  );
}

function TableRow({ className, ...props }: React.ComponentProps<"tr">) {
  return (
    <tr
      data-slot="table-row"
      className={cn(
        "border-border/20 border-b transition-all duration-200",
        "hover:bg-gradient-to-r hover:from-fuchsia-500/5 hover:to-cyan-400/5 hover:shadow-sm",
        "data-[state=selected]:bg-gradient-to-r data-[state=selected]:from-fuchsia-500/10 data-[state=selected]:to-cyan-400/10",
        "data-[state=selected]:border-fuchsia-500/20 data-[state=selected]:shadow-md",
        className,
      )}
      {...props}
    />
  );
}

function TableHead({ className, ...props }: React.ComponentProps<"th">) {
  return (
    <th
      data-slot="table-head"
      className={cn(
        "text-foreground/90 h-12 px-4 text-start align-middle font-semibold whitespace-nowrap",
        "[&:has([role=checkbox])]:pe-0 [&>[role=checkbox]]:translate-y-[2px]",
        "bg-gradient-to-r from-transparent to-transparent hover:from-fuchsia-500/5 hover:to-cyan-400/5",
        "transition-all duration-200",
        className,
      )}
      {...props}
    />
  );
}

function TableCell({ className, ...props }: React.ComponentProps<"td">) {
  return (
    <td
      data-slot="table-cell"
      className={cn(
        "text-foreground/80 px-4 py-3 align-middle whitespace-nowrap",
        "[&:has([role=checkbox])]:pe-0 [&>[role=checkbox]]:translate-y-[2px]",
        className,
      )}
      {...props}
    />
  );
}

function TableCaption({
  className,
  ...props
}: React.ComponentProps<"caption">) {
  return (
    <caption
      data-slot="table-caption"
      className={cn("text-muted-foreground mt-4 text-sm", className)}
      {...props}
    />
  );
}

export {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
};
