import * as ScrollAreaPrimitive from "@radix-ui/react-scroll-area";
import * as React from "react";

import { cn } from "@/lib/utils";

function ScrollArea({
  className,
  children,
  ...props
}: React.ComponentProps<typeof ScrollAreaPrimitive.Root>) {
  return (
    <ScrollAreaPrimitive.Root
      data-slot="scroll-area"
      className={cn("relative", className)}
      {...props}
    >
      <ScrollAreaPrimitive.Viewport
        data-slot="scroll-area-viewport"
        className={cn(
          "size-full rounded-[inherit] transition-[color,box-shadow] outline-none",
          "focus-visible:ring-[3px] focus-visible:ring-fuchsia-500/50 focus-visible:outline-1",
          "scroll-smooth",
        )}
      >
        {children}
      </ScrollAreaPrimitive.Viewport>
      <ScrollBar />
      <ScrollAreaPrimitive.Corner className="bg-transparent" />
    </ScrollAreaPrimitive.Root>
  );
}

function ScrollBar({
  className,
  orientation = "vertical",
  ...props
}: React.ComponentProps<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>) {
  return (
    <ScrollAreaPrimitive.ScrollAreaScrollbar
      data-slot="scroll-area-scrollbar"
      orientation={orientation}
      className={cn(
        "flex touch-none transition-all duration-300 select-none",
        "hover:bg-gradient-to-b hover:from-fuchsia-500/5 hover:to-cyan-400/5",
        orientation === "vertical" &&
          "h-full w-3 border-l border-l-transparent p-[1px]",
        orientation === "horizontal" &&
          "h-3 flex-col border-t border-t-transparent p-[1px]",
        className,
      )}
      {...props}
    >
      <ScrollAreaPrimitive.ScrollAreaThumb
        data-slot="scroll-area-thumb"
        className={cn(
          "relative flex-1 rounded-full transition-all duration-300",
          "bg-gradient-to-b from-fuchsia-500/40 to-cyan-400/40",
          "hover:from-fuchsia-500/60 hover:to-cyan-400/60",
          "hover:scale-110 hover:shadow-lg hover:shadow-fuchsia-500/20",
          "active:from-fuchsia-500/80 active:to-cyan-400/80",
          "border border-white/10",
        )}
      />
    </ScrollAreaPrimitive.ScrollAreaScrollbar>
  );
}

export { ScrollArea, ScrollBar };
