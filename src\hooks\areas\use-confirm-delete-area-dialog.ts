import { openConfirmDeleteAreaDialogAtom } from "@/atoms/app/open-atoms";
import {
  areaShapePointsAtom,
  selectedAreaToDeleteAtom,
  selectedDrawerTypeAtom,
} from "@/atoms/app/selected-atoms";
import { areasAtom } from "@/atoms/entities/areas-atom";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

export default function useConfirmDeleteAreaDialog() {
  const { t } = useTranslation();
  const isOpened = openConfirmDeleteAreaDialogAtom.useOpened();

  const { area } = areasAtom.useValue();
  const { slug } = selectedAreaToDeleteAtom.useValue();

  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingFetchArea, setIsLoadingFetchArea] = useState(false);

  const closeDialog = () => {
    openConfirmDeleteAreaDialogAtom.close();
    selectedAreaToDeleteAtom.reset();
    areasAtom.change("area", null);

    selectedDrawerTypeAtom.reset();
    areaShapePointsAtom.reset();
  };

  const deleteArea = async () => {
    if (slug) {
      setIsLoading(true);
      await areasAtom.deleteArea(slug, closeDialog);
      setIsLoading(false);
    }
  };

  useEffect(() => {
    async function fetchArea() {
      if (slug) {
        setIsLoadingFetchArea(true);
        await areasAtom.getOneArea(slug);
        setIsLoadingFetchArea(false);
      }
    }

    fetchArea();
  }, [slug]);

  return {
    t,
    isOpened,
    area,
    isLoading,
    isLoadingFetchArea,
    closeDialog,
    deleteArea,
  };
}
