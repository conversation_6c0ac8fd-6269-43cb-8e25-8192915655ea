import { openAtom } from "@mongez/react-atom";

/***
 * Users
 */
export const openCreateUserAtom = openAtom("open-create-user-atom");
export const openUpdateUserAtom = openAtom("open-update-user-atom");
export const openConfirmDeleteUserAtom = openAtom(
  "open-confirm-delete-user-atom",
);

/***
 * Units
 */
export const openCreateUnitAtom = openAtom("open-create-unit-atom");
export const openUpdateUnitAtom = openAtom("open-update-unit-atom");
export const openConfirmDeleteUnitAtom = openAtom(
  "open-confirm-delete-unit-atom",
);

/**
 * Trailers
 */
export const openCreateTrailerAtom = openAtom("open-create-trailer-atom");
export const openUpdateTrailerAtom = openAtom("open-update-trailer-atom");
export const openConfirmDeleteTrailerAtom = openAtom(
  "open-confirm-delete-trailer-atom",
);

/**
 * Drivers
 */
export const openCreateDriverAtom = openAtom("open-create-driver-atom");
export const openUpdateDriverAtom = openAtom("open-update-driver-atom");
export const openConfirmDeleteDriverAtom = openAtom(
  "open-confirm-delete-driver-atom",
);

/**
 * Areas
 */
export const openConfirmDeleteAreaDialogAtom = openAtom(
  "open-confirm-delete-area-dialog-atom",
);

/**
 * auth
 */
export const openConfirmLogoutDialogAtom = openAtom(
  "open-confirm-logout-dialog-atom",
);
