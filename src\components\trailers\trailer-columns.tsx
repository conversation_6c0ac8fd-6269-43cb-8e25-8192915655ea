import {
  openConfirmDeleteTrailerAtom,
  openUpdateTrailerAtom,
} from "@/atoms/app/open-atoms";
import { selectedTrailerAtom } from "@/atoms/app/selected-atoms";
import type { Trailer } from "@/types/trailers";
import type { TFunction } from "i18next";
import { Edit, Trash2 } from "lucide-react";
import { createColumns } from "../utils/columns-factory";

export const TrailerColumns = (t: TFunction) =>
  createColumns<Trailer>({
    t,
    entityKey: "trailers",
    fields: [
      { key: "name", title: "common.table.name" },
      {
        key: "is_active",
        title: "trailers.table.isActive",
        cell: (trailer) =>
          trailer.is_active
            ? t("common.boolean.true")
            : t("common.boolean.false"),
      },
    ],
    actions: [
      {
        icon: <Edit />,
        label: t("common.actions.edit"),
        onClick: (row) => {
          selectedTrailerAtom.change("slug", row.slug);
          openUpdateTrailerAtom.open();
        },
      },
      {
        icon: <Trash2 color="red" />,
        label: t("common.actions.delete"),
        onClick: (row) => {
          selectedTrailerAtom.change("slug", row.slug);
          openConfirmDeleteTrailerAtom.open();
        },
      },
    ],
  });
