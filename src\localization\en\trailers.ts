export const trailersTranslations = {
  title: "Trailers",
  add: "Add Trailer",

  confirm_delete: {
    deleteTrailerTitle: "Delete Trailer",
    deleteTrailerDescription:
      "Are you sure you want to delete this trailer? This action cannot be undone.",
  },

  create_dialog: {
    title: "Create Trailer",
    description: "Fill in the details to create a new trailer.",
    form: {
      name_placeholder: "Enter trailer name",
      description_placeholder: "Enter trailer description",
      is_active: "Is Active",
    },
  },

  edit_dialog: {
    title: "Edit Trailer",
    description: "Fill in the details to edit the trailer.",
    form: {
      name_placeholder: "Enter trailer name",
      description_placeholder: "Enter trailer description",
      is_active: "Is Active",
    },
  },

  table: {
    selectAll: "Select all",
    selectRow: "Select row",
    isActive: "Is Active",
  },

  actions: {
    title: "Actions",
  },

  validation: {
    name_required: "Name is required",
    description_required: "Description is required",
  },
};
