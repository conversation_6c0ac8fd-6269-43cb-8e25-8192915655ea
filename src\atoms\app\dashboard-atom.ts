import type { DashboardStats, GetStatsResponse } from "@/types/dashboard";
import { endpoint } from "@/utils/endpoints";
import { atom } from "@mongez/react-atom";
import { AxiosError } from "axios";
import toast from "react-hot-toast";

type DashboardAtom = {
  stats: DashboardStats | null;
  loading: boolean;
};

type DashboardAtomActions = {
  getStats: () => void;
};

export const dashboardAtom = atom<DashboardAtom, DashboardAtomActions>({
  key: "dashboard-atom",
  default: {
    stats: null,
    loading: true,
  },
  actions: {
    getStats: async () => {
      try {
        const { data } = await endpoint.get<GetStatsResponse>("home/stats");
        dashboardAtom.change("stats", data.data);
      } catch (error) {
        if (error instanceof AxiosError) {
          toast.error(error.response?.data.message);
        } else {
          toast.error((error as Error).message);
        }
      } finally {
        dashboardAtom.change("loading", false);
      }
    },
  },
});
