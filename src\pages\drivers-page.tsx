import { openCreateDriverAtom } from "@/atoms/app/open-atoms";
import { driversAtom } from "@/atoms/entities/drivers-atom";
import ConfirmDeleteDriverDialog from "@/components/drivers/confirm-delete-driver-dialog";
import CreateDriverDialog from "@/components/drivers/create-driver-dialog";
import { DriverColumns } from "@/components/drivers/driver-columns";
import EditDriverDialog from "@/components/drivers/edit-driver-dialog";
import { DataTable } from "@/components/table/data-table";
import GenericEntityPage from "@/components/utils/generic-entity-page";
import { useTranslation } from "react-i18next";

export default function DriversPage() {
  const { t } = useTranslation();
  const { drivers } = driversAtom.useValue();

  return (
    <GenericEntityPage
      title="drivers.title"
      addText="drivers.add"
      onAddClick={openCreateDriverAtom.open}
      leftContent={<DataTable columns={DriverColumns(t)} data={drivers} />}
      dialogs={
        <>
          <CreateDriverDialog />
          <EditDriverDialog />
          <ConfirmDeleteDriverDialog />
        </>
      }
      fetchers={[driversAtom.getDrivers]}
    />
  );
}
