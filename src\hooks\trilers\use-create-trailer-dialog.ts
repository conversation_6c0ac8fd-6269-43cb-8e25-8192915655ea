import { openCreateTrailer<PERSON>tom } from "@/atoms/app/open-atoms";
import { trailresAtom } from "@/atoms/entities/trailers-atom";
import type { CreateTrailerFormData } from "@/types/trailers";
import { zodResolver } from "@hookform/resolvers/zod";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { z } from "zod";

export default function useCreateTrailerDialog() {
  const { t } = useTranslation();

  const FormSchema = z.object({
    name: z.string().nonempty(t("trailers.validation.name_required")),
    description: z
      .string()
      .nonempty(t("trailers.validation.description_required")),
    is_active: z.boolean(),
  });

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      name: "",
      description: "",
      is_active: false,
    },
  });

  const [isLoading, setIsLoading] = useState(false);
  const isOpened = openCreateTrailerAtom.useOpened();

  const closeDialog = () => {
    openCreateTrailerAtom.close();
    form.reset();
  };

  async function onSubmit(data: z.infer<typeof FormSchema>) {
    const formData: CreateTrailerFormData = {
      name: data.name,
      description: data.description,
      is_active: data.is_active ? 1 : 0,
    };

    setIsLoading(true);

    await trailresAtom.createTrailer(formData, () => {
      closeDialog();
    });

    setIsLoading(false);
  }

  return {
    form,
    isLoading,
    isOpened,
    closeDialog,
    onSubmit,
    t,
  };
}
