export const driversTranslations = {
  title: "السائقون",
  add: "إضافة سائق",

  confirm_delete: {
    deleteDriverTitle: "حذف السائق",
    deleteDriverDescription:
      "هل أنت متأكد أنك تريد حذف هذا السائق؟ لا يمكن التراجع عن هذا الإجراء.",
  },

  dialog: {
    createTitle: "إنشاء سائق",
    createDescription: "املأ التفاصيل لإنشاء سائق جديد.",
    editTitle: "تعديل السائق",
    editDescription: "قم بتحديث تفاصيل السائق أدناه.",
  },

  form: {
    namePlaceholder: "اسم السائق",
    driverType: "نوع السائق",
    driverTypePlaceholder: "نوع السائق",
    employee: "موظ<PERSON>",
    subcontractor: "متعهد فرعي",
    nationalId: "الرقم القومي",
    nationalIdPlaceholder: "الرقم القومي",
    nationality: "الجنسية",
    nationalityPlaceholder: "الجنسية",
    identityNumber: "رقم الهوية",
    identityNumberPlaceholder: "رقم الهوية",
    address: "العنوان",
    addressPlaceholder: "عنوان السائق",
    isActive: "نشط",
    isActiveDescription: "هل هذا السائق نشط؟",
    no_driver_type_found: "لم يتم العثور على نوع سائق",
  },

  actions: {
    create: "إنشاء",
    creating: "جارٍ الإنشاء...",
    update: "تحديث السائق",
    updating: "جارٍ التحديث...",
  },

  validation: {
    name: "الاسم مطلوب",
    driverType: "نوع السائق مطلوب",
    nationalId: "الرقم القومي مطلوب",
    nationality: "الجنسية مطلوبة",
    identityNumber: "رقم الهوية مطلوب",
    address: "العنوان مطلوب",
  },

  table: {
    selectAll: "تحديد الكل",
    selectRow: "تحديد الصف",
    isActive: "نشط",
  },

  actions: {
    title: "إجراءات",
  },
};
